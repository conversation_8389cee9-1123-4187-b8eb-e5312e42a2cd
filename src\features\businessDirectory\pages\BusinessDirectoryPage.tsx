import React, { useEffect, useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { MapPin, Globe, Calendar, Building2, Search, X, Shield, Filter, ChevronDown, ChevronRight, ArrowUpDown } from "lucide-react";
import { supabase } from '@/integrations/supabase/client';
import { getIndustryTheme, IndustryIcon } from '@/utils/industryIcons';
import { getNetZeroCategoryTheme, getNetZeroCategoryThemeById, NetZeroCategoryIcon } from '@/utils/netZeroCategoryIcons';
import BusinessLogoDisplay from '../components/BusinessLogoDisplay';
import NetZeroCategoryCards from '../components/NetZeroCategoryCards';
import { NetZeroCategoryService } from '@/services/netZeroCategoryService';
import { UKIndustryService } from '@/services/ukIndustryService';
import type { Database } from '@/types/database.types';
import type { NetZeroSubcategoryWithCategory, NetZeroCategoryWithSubcategories } from '@/types/netzero-categories.types';
import type { UKIndustryWithChildren } from '@/types/uk-industries.types';

type Business = Database['public']['Tables']['businesses']['Row'];

// Extended business type with location info
interface BusinessWithLocation extends Business {
  headquarters_location?: {
    id: string;
    name: string;
    slug: string;
    path: string;
  } | null;
  customer_locations?: Array<{
    id: string;
    name: string;
    slug: string;
    type: string;
    path: string;
  }> | null;
}

// Sort options for businesses
export type BusinessSortOption = 'name-asc' | 'name-desc' | 'joined-asc' | 'joined-desc';

// Filter state interface
interface FilterState {
  showFilters: boolean;
  verifiedOnly: boolean;
  selectedMainIndustries: string[];
  selectedTargetIndustries: string[];
  selectedNetZeroCategories: string[];
  selectedCategoryId: string; // For category card filtering
  expandedIndustryCategories: Set<string>;
  expandedNetZeroCategories: Set<string>;
  sortBy: BusinessSortOption;
}

const BusinessDirectoryPage: React.FC = () => {
  const navigate = useNavigate();
  const [businesses, setBusinesses] = useState<any[]>([]); // Using any for now to bypass TypeScript issues
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [industries, setIndustries] = useState<UKIndustryWithChildren[]>([]);
  const [loadingIndustries, setLoadingIndustries] = useState(true);
  const [netZeroCategories, setNetZeroCategories] = useState<NetZeroCategoryWithSubcategories[]>([]);
  const [loadingNetZeroCategories, setLoadingNetZeroCategories] = useState(true);
  
  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    showFilters: false,
    verifiedOnly: false,
    selectedMainIndustries: [],
    selectedTargetIndustries: [],
    selectedNetZeroCategories: [],
    selectedCategoryId: '',
    expandedIndustryCategories: new Set(),
    expandedNetZeroCategories: new Set(),
    sortBy: 'name-asc'
  });

  // Load industries and net-zero categories for filtering
  useEffect(() => {
    const loadIndustries = async () => {
      try {
        setLoadingIndustries(true);
        const industriesData = await UKIndustryService.getAllIndustriesWithChildren();
        setIndustries(industriesData);
      } catch (err) {
        console.error('Failed to load industries:', err);
      } finally {
        setLoadingIndustries(false);
      }
    };

    const loadNetZeroCategories = async () => {
      try {
        setLoadingNetZeroCategories(true);
        const categoriesData = await NetZeroCategoryService.getAllCategoriesWithSubcategories();
        setNetZeroCategories(categoriesData);
      } catch (err) {
        console.error('Failed to load net-zero categories:', err);
      } finally {
        setLoadingNetZeroCategories(false);
      }
    };

    loadIndustries();
    loadNetZeroCategories();
  }, []);

  useEffect(() => {
    const fetchBusinessesWithLocations = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get all businesses with their full details - using any to bypass TypeScript issues
        const { data: businessData, error: businessError } = await supabase
          .from('businesses' as any)
          .select('*')
          .order('business_name');

        if (businessError) throw businessError;

        console.log('Business data sample:', businessData?.[0]); // Debug log

        // For each business, try to get headquarters location details manually
        const businessesWithLocations = await Promise.all(
          (businessData || []).map(async (business: any) => {
            let headquartersLocation = null;
            if (business.headquarters_location_id) {
              try {
                // Get headquarters location details - using any to bypass TypeScript issues
                const { data: hqLocationData, error: hqLocationError } = await supabase
                  .from('locations' as any)
                  .select('id, name, slug, type')
                  .eq('id', business.headquarters_location_id)
                  .single();

                if (!hqLocationError && hqLocationData) {
                  console.log('HQ Location data for', business.business_name, ':', hqLocationData); // Debug log
                  const locationInfo = hqLocationData as any; // Cast to any to bypass TypeScript issues
                  headquartersLocation = {
                    headquarters_name: locationInfo.name,
                    headquarters_slug: locationInfo.slug,
                    headquarters_id: locationInfo.id,
                    headquarters_path: null // We'll calculate this later if needed
                  };
                }
              } catch (err) {
                console.warn('Could not fetch HQ location for business:', business.business_name, err);
              }
            }

            // Fetch net-zero categories for each business
            let netZeroCategories = [];
            let primaryNetZeroCategory = undefined;
            try {
              const { categories, primary_category } = await NetZeroCategoryService.getBusinessCategories(business.id);
              netZeroCategories = categories;
              primaryNetZeroCategory = primary_category;
            } catch (err) {
              console.warn('Could not fetch net-zero categories for business:', business.business_name, err);
            }

            // Fetch main industry and target industries with better error handling
            let mainIndustry = null;
            let targetIndustries = [];
            
            try {
              // Fetch main industry if business has main_industry_id
              if (business.main_industry_id) {
                try {
                  const { data: mainIndustryData, error: mainIndustryError } = await supabase
                    .from('uk_industries')
                    .select('*')
                    .eq('id', business.main_industry_id)
                    .maybeSingle();

                  if (!mainIndustryError && mainIndustryData) {
                    // Fetch parent separately if it exists
                    let parent = null;
                    if (mainIndustryData.parent_id) {
                      const { data: parentData, error: parentError } = await supabase
                        .from('uk_industries')
                        .select('*')
                        .eq('id', mainIndustryData.parent_id)
                        .maybeSingle();
                      
                      if (!parentError && parentData) {
                        parent = parentData;
                      }
                    }
                    
                    mainIndustry = {
                      ...mainIndustryData,
                      parent
                    };
                  } else if (mainIndustryError) {
                    console.error('Error fetching main industry:', mainIndustryError);
                  }
                } catch (mainIndustryErr) {
                  console.error('Exception fetching main industry for business:', business.business_name, mainIndustryErr);
                }
              }

              // Fetch target industries
              try {
                const { data: targetIndustriesData, error: targetError } = await supabase
                  .from('business_target_industries')
                  .select('industry_id')
                  .eq('business_id', business.id);

                if (!targetError && targetIndustriesData && targetIndustriesData.length > 0) {
                  // Get the industry IDs
                  const industryIds = targetIndustriesData.map(item => item.industry_id);
                  
                  // Fetch the industry details with parent data
                  const { data: industriesData, error: industriesError } = await supabase
                    .from('uk_industries')
                    .select('*')
                    .in('id', industryIds);

                  if (!industriesError && industriesData) {
                    // Fetch parents for each industry separately
                    targetIndustries = await Promise.all(
                      industriesData.map(async (industry) => {
                        let parent = null;
                        if (industry.parent_id) {
                          const { data: parentData, error: parentError } = await supabase
                            .from('uk_industries')
                            .select('*')
                            .eq('id', industry.parent_id)
                            .maybeSingle();
                          
                          if (!parentError && parentData) {
                            parent = parentData;
                          }
                        }
                        
                        return {
                          ...industry,
                          parent
                        };
                      })
                    );
                  } else if (industriesError) {
                    console.error('Error fetching target industries details:', industriesError);
                  }
                } else if (targetError) {
                  console.error('Error fetching target industries:', targetError);
                }
              } catch (targetIndustriesErr) {
                console.error('Exception fetching target industries for business:', business.business_name, targetIndustriesErr);
              }
            } catch (err) {
              console.error('Exception in industry fetching for business:', business.business_name, err);
            }



            return {
              ...business,
              headquarters_location: headquartersLocation,
              netZeroCategories,
              primaryNetZeroCategory,
              mainIndustry,
              targetIndustries
            };
          })
        );


        
        setBusinesses(businessesWithLocations);
      } catch (err) {
        console.error('Failed to fetch businesses:', err);
        setError(err instanceof Error ? err.message : 'Failed to load businesses');
      } finally {
        setLoading(false);
      }
    };

    fetchBusinessesWithLocations();
  }, []);



  // Filter and sort businesses based on current filters and sort option
  const filteredAndSortedBusinesses = useMemo(() => {
    // First filter
    const filtered = businesses.filter((business) => {
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matches = business.business_name?.toLowerCase().includes(searchLower) ||
          business.company_description?.toLowerCase().includes(searchLower) ||
          business.website?.toLowerCase().includes(searchLower) ||
          (business.headquarters_location?.headquarters_name?.toLowerCase().includes(searchLower)) ||
          
          // Search in main industry
          (business.mainIndustry?.name?.toLowerCase().includes(searchLower)) ||
          (business.mainIndustry?.parent?.name?.toLowerCase().includes(searchLower)) ||
          
          // Search in target industries
          (business.targetIndustries?.some((industry: any) =>
            industry.name?.toLowerCase().includes(searchLower) ||
            industry.parent?.name?.toLowerCase().includes(searchLower)
          )) ||
          
          // Search in net-zero categories
          (business.primaryNetZeroCategory?.name?.toLowerCase().includes(searchLower)) ||
          (business.netZeroCategories?.some((category: any) =>
            category.name?.toLowerCase().includes(searchLower)
          ));
        
        if (!matches) {
          return false;
        }
      }

      // Verified filter
      if (filters.verifiedOnly && !business.is_verified) {
        return false;
      }

      // Main industry filter
      if (filters.selectedMainIndustries.length > 0) {
        if (business.mainIndustry) {
          const isMatch = filters.selectedMainIndustries.includes(business.mainIndustry.id);
          if (!isMatch) {
            return false;
          }
        } else {
          return false;
        }
      }

      // Target industries filter
      if (filters.selectedTargetIndustries.length > 0) {
        const hasMatchingTargetIndustry = business.targetIndustries?.some((industry: any) =>
          filters.selectedTargetIndustries.includes(industry.id)
        );
        if (!hasMatchingTargetIndustry) {
          return false;
        }
      }

      // Net-zero categories filter
      if (filters.selectedNetZeroCategories.length > 0) {
        const hasMatchingNetZeroCategory = business.netZeroCategories?.some((category: any) =>
          filters.selectedNetZeroCategories.includes(category.id)
        );
        if (!hasMatchingNetZeroCategory) {
          return false;
        }
      }

      // Category card filter (main Net Zero category)
      if (filters.selectedCategoryId) {
        const hasMatchingMainCategory = business.netZeroCategories?.some((subcategory: any) =>
          subcategory.category?.id === filters.selectedCategoryId
        );
        if (!hasMatchingMainCategory) {
          return false;
        }
      }

      return true;
    });

    // Then sort
    return filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'name-asc':
          return (a.business_name || '').localeCompare(b.business_name || '');
        case 'name-desc':
          return (b.business_name || '').localeCompare(a.business_name || '');
        case 'joined-asc':
          return new Date(a.created_at || 0).getTime() - new Date(b.created_at || 0).getTime();
        case 'joined-desc':
          return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
        default:
          return 0;
      }
    });
  }, [businesses, searchTerm, filters]);



  // Filter helper functions
  const toggleFilter = (filterKey: keyof FilterState, value?: any) => {
    setFilters(prev => ({
      ...prev,
      [filterKey]: value !== undefined ? value : !prev[filterKey]
    }));
  };

  const toggleMainIndustry = (industryId: string) => {
    setFilters(prev => ({
      ...prev,
      selectedMainIndustries: prev.selectedMainIndustries.includes(industryId)
        ? prev.selectedMainIndustries.filter(id => id !== industryId)
        : [...prev.selectedMainIndustries, industryId]
    }));
  };

  const toggleTargetIndustry = (industryId: string) => {
    setFilters(prev => ({
      ...prev,
      selectedTargetIndustries: prev.selectedTargetIndustries.includes(industryId)
        ? prev.selectedTargetIndustries.filter(id => id !== industryId)
        : [...prev.selectedTargetIndustries, industryId]
    }));
  };

  const toggleNetZeroCategory = (subcategoryId: string) => {
    setFilters(prev => ({
      ...prev,
      selectedNetZeroCategories: prev.selectedNetZeroCategories.includes(subcategoryId)
        ? prev.selectedNetZeroCategories.filter(id => id !== subcategoryId)
        : [...prev.selectedNetZeroCategories, subcategoryId]
    }));
  };

  const handleCategorySelect = (categoryId: string, categoryName: string) => {
    setFilters(prev => ({
      ...prev,
      selectedCategoryId: prev.selectedCategoryId === categoryId ? '' : categoryId
    }));
  };

  const toggleIndustryCategory = (categoryId: string) => {
    setFilters(prev => {
      const newExpanded = new Set(prev.expandedIndustryCategories);
      if (newExpanded.has(categoryId)) {
        newExpanded.delete(categoryId);
      } else {
        newExpanded.add(categoryId);
      }
      return {
        ...prev,
        expandedIndustryCategories: newExpanded
      };
    });
  };

  const toggleNetZeroCategoryExpansion = (categoryId: string) => {
    setFilters(prev => {
      const newExpanded = new Set(prev.expandedNetZeroCategories);
      if (newExpanded.has(categoryId)) {
        newExpanded.delete(categoryId);
      } else {
        newExpanded.add(categoryId);
      }
      return {
        ...prev,
        expandedNetZeroCategories: newExpanded
      };
    });
  };

  const clearAllFilters = () => {
    setFilters({
      showFilters: false,
      verifiedOnly: false,
      selectedMainIndustries: [],
      selectedTargetIndustries: [],
      selectedNetZeroCategories: [],
      selectedCategoryId: '',
      expandedIndustryCategories: new Set(),
      expandedNetZeroCategories: new Set(),
      sortBy: 'name-asc'
    });
    setSearchTerm('');
  };

  const hasActiveFilters = () => {
    return searchTerm ||
           filters.verifiedOnly ||
           filters.selectedMainIndustries.length > 0 ||
           filters.selectedTargetIndustries.length > 0 ||
           filters.selectedNetZeroCategories.length > 0 ||
           filters.selectedCategoryId;
  };

  const renderIndustryFilter = (
    title: string,
    selectedIndustries: string[],
    onToggle: (id: string) => void
  ) => {
    const selectAllInCategory = (parentId: string) => {
      const parent = industries.find(p => p.id === parentId);
      if (!parent) return;
      
      const allChildIds = parent.children.map(child => child.id);
      const allSelected = allChildIds.every(id => selectedIndustries.includes(id));
      
      if (allSelected) {
        // Deselect all in this category
        allChildIds.forEach(id => {
          if (selectedIndustries.includes(id)) {
            onToggle(id);
          }
        });
      } else {
        // Select all in this category
        allChildIds.forEach(id => {
          if (!selectedIndustries.includes(id)) {
            onToggle(id);
          }
        });
      }
    };

    const selectAllIndustries = () => {
      const allIndustryIds = industries.flatMap(p => p.children.map(c => c.id));
      const allSelected = allIndustryIds.every(id => selectedIndustries.includes(id));
      
      if (allSelected) {
        // Deselect all
        selectedIndustries.forEach(id => onToggle(id));
      } else {
        // Select all
        allIndustryIds.forEach(id => {
          if (!selectedIndustries.includes(id)) {
            onToggle(id);
          }
        });
      }
    };

    const totalSelected = selectedIndustries.length;
    const totalAvailable = industries.reduce((sum, parent) => sum + parent.children.length, 0);

    return (
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
          <div className="space-y-1 flex-1">
            <Label className="text-sm font-semibold">{title}</Label>
            <p className="text-xs text-muted-foreground hidden sm:block">
              {title === "Business Industry" 
                ? "Primary industry the business operates in"
                : "Industries the business serves or is relevant to"
              }
            </p>
          </div>
          <div className="flex flex-row items-center justify-between sm:flex-col sm:items-end gap-2 w-full sm:w-auto">
            <span className="text-xs text-muted-foreground">
              {totalSelected} of {totalAvailable} selected
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={selectAllIndustries}
              className="h-8 px-3 text-xs min-w-[80px]"
            >
              {totalSelected === totalAvailable ? 'Clear All' : 'Select All'}
            </Button>
          </div>
        </div>
        
        <div className="border rounded-lg bg-card">
          <div className="space-y-1 max-h-64 overflow-y-auto p-2 sm:p-3">
            {industries.map((parent, index) => {
              const isExpanded = filters.expandedIndustryCategories.has(parent.id);
              const selectedInCategory = parent.children.filter(child => 
                selectedIndustries.includes(child.id)
              ).length;
              const allInCategorySelected = selectedInCategory === parent.children.length;
              const someInCategorySelected = selectedInCategory > 0 && selectedInCategory < parent.children.length;
              
              return (
                <div key={parent.id} className="space-y-1">
                  <Collapsible open={isExpanded} onOpenChange={() => toggleIndustryCategory(parent.id)}>
                    <div className={`flex items-start gap-1 sm:gap-2 p-1.5 sm:p-2 rounded-md border border-transparent min-h-[48px] overflow-hidden ${getIndustryTheme(parent.name).borderColor} ${getIndustryTheme(parent.name).hoverBgColor}`}>
                      <CollapsibleTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="flex-1 justify-start p-0 h-auto font-normal min-h-[44px] text-left min-w-0 hover:bg-transparent"
                        >
                          <div className="flex items-start gap-1.5 sm:gap-2 flex-1 min-w-0 overflow-hidden">
                            <div className="flex-shrink-0 mt-0.5 sm:mt-1 flex items-center gap-1">
                              <IndustryIcon 
                                industryName={parent.name} 
                                size="md" 
                                className="w-4 h-4 sm:w-5 sm:h-5" 
                              />
                              {isExpanded ? (
                                <ChevronDown className="w-3 h-3 sm:w-4 sm:h-4 text-muted-foreground" />
                              ) : (
                                <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4 text-muted-foreground" />
                              )}
                            </div>
                            <span className={`text-xs sm:text-sm font-medium text-left break-words leading-4 sm:leading-5 min-w-0 overflow-hidden hyphens-auto ${getIndustryTheme(parent.name).textColor}`}>{parent.name}</span>
                          </div>
                        </Button>
                      </CollapsibleTrigger>
                      
                      <div className="flex flex-row sm:flex-col items-center sm:items-end gap-1 flex-shrink-0 w-auto">
                        {selectedInCategory > 0 && (
                          <Badge variant="secondary" className="text-xs px-1 py-0.5 min-w-[20px] text-center leading-none">
                            {selectedInCategory}
                          </Badge>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => selectAllInCategory(parent.id)}
                          className={`h-7 px-1.5 sm:px-2 text-xs min-w-[40px] sm:min-w-[50px] whitespace-nowrap text-center ${getIndustryTheme(parent.name).borderColor} ${getIndustryTheme(parent.name).textColor} hover:bg-white`}
                        >
                          {allInCategorySelected ? 'None' : 'All'}
                        </Button>
                      </div>
                    </div>
                    
                    <CollapsibleContent className="space-y-1">
                      <div className="ml-4 sm:ml-6 space-y-1 border-l-2 border-muted pl-2 sm:pl-4">
                        {parent.children.map((child) => (
                          <div key={child.id} className="flex items-center space-x-2 sm:space-x-3 p-2 sm:p-3 rounded hover:bg-muted/30 min-h-[44px] sm:min-h-[48px] touch-manipulation overflow-hidden">
                            <Checkbox
                              id={`${title}-${child.id}`}
                              checked={selectedIndustries.includes(child.id)}
                              onCheckedChange={() => onToggle(child.id)}
                              className="shrink-0 h-4 w-4"
                            />
                            <Label
                              htmlFor={`${title}-${child.id}`}
                              className="text-xs sm:text-sm cursor-pointer flex-1 leading-relaxed select-none min-w-0 break-words"
                            >
                              {child.name}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                  {index < industries.length - 1 && (
                    <div className="mx-2 border-b border-muted/50"></div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  const renderNetZeroCategoriesFilter = () => {
    const selectAllInCategory = (categoryId: string) => {
      const category = netZeroCategories.find(c => c.id === categoryId);
      if (!category) return;
      
      const allSubcategoryIds = category.subcategories.map(sub => sub.id);
      const allSelected = allSubcategoryIds.every(id => filters.selectedNetZeroCategories.includes(id));
      
      if (allSelected) {
        // Deselect all in this category
        allSubcategoryIds.forEach(id => {
          if (filters.selectedNetZeroCategories.includes(id)) {
            toggleNetZeroCategory(id);
          }
        });
      } else {
        // Select all in this category
        allSubcategoryIds.forEach(id => {
          if (!filters.selectedNetZeroCategories.includes(id)) {
            toggleNetZeroCategory(id);
          }
        });
      }
    };

    const selectAllCategories = () => {
      const allSubcategoryIds = netZeroCategories.flatMap(c => c.subcategories.map(s => s.id));
      const allSelected = allSubcategoryIds.every(id => filters.selectedNetZeroCategories.includes(id));
      
      if (allSelected) {
        // Deselect all
        filters.selectedNetZeroCategories.forEach(id => toggleNetZeroCategory(id));
      } else {
        // Select all
        allSubcategoryIds.forEach(id => {
          if (!filters.selectedNetZeroCategories.includes(id)) {
            toggleNetZeroCategory(id);
          }
        });
      }
    };

    const totalSelected = filters.selectedNetZeroCategories.length;
    const totalAvailable = netZeroCategories.reduce((sum, category) => sum + category.subcategories.length, 0);

    return (
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
          <div className="space-y-1 flex-1">
            <Label className="text-sm font-semibold">Net-Zero Focus Areas</Label>
            <p className="text-xs text-muted-foreground hidden sm:block">
              Main net-zero categories the business focuses on
            </p>
          </div>
          <div className="flex flex-row items-center justify-between sm:flex-col sm:items-end gap-2 w-full sm:w-auto">
            <span className="text-xs text-muted-foreground">
              {totalSelected} of {totalAvailable} selected
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={selectAllCategories}
              className="h-8 px-3 text-xs min-w-[80px]"
            >
              {totalSelected === totalAvailable ? 'Clear All' : 'Select All'}
            </Button>
          </div>
        </div>
        
        <div className="border rounded-lg bg-card">
          <div className="space-y-1 max-h-64 overflow-y-auto p-2 sm:p-3">
            {netZeroCategories.map((category, index) => {
              const isExpanded = filters.expandedNetZeroCategories.has(category.id);
              const selectedInCategory = category.subcategories.filter(sub => 
                filters.selectedNetZeroCategories.includes(sub.id)
              ).length;
              const allInCategorySelected = selectedInCategory === category.subcategories.length;
              
              return (
                <div key={category.id} className="space-y-1">
                  <Collapsible open={isExpanded} onOpenChange={() => toggleNetZeroCategoryExpansion(category.id)}>
                    <div className={`flex items-start gap-1 sm:gap-2 p-1.5 sm:p-2 rounded-md border min-h-[48px] overflow-hidden ${getNetZeroCategoryTheme(category.name).borderColor} ${getNetZeroCategoryTheme(category.name).hoverBgColor}`}>
                      <CollapsibleTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="flex-1 justify-start p-0 h-auto font-normal min-h-[44px] text-left min-w-0 hover:bg-transparent"
                        >
                          <div className="flex items-start gap-1.5 sm:gap-2 flex-1 min-w-0 overflow-hidden">
                            <div className="flex-shrink-0 mt-0.5 sm:mt-1 flex items-center gap-1">
                              <NetZeroCategoryIcon 
                                categoryName={category.name} 
                                size="md" 
                                className="w-4 h-4 sm:w-5 sm:h-5" 
                              />
                              {isExpanded ? (
                                <ChevronDown className="w-3 h-3 sm:w-4 sm:h-4 text-muted-foreground" />
                              ) : (
                                <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4 text-muted-foreground" />
                              )}
                            </div>
                            <span className={`text-xs sm:text-sm font-medium text-left break-words leading-4 sm:leading-5 min-w-0 overflow-hidden hyphens-auto ${getNetZeroCategoryTheme(category.name).textColor}`}>{category.name}</span>
                          </div>
                        </Button>
                      </CollapsibleTrigger>
                      
                      <div className="flex flex-row sm:flex-col items-center sm:items-end gap-1 flex-shrink-0 w-auto">
                        {selectedInCategory > 0 && (
                          <Badge variant="secondary" className="text-xs px-1 py-0.5 min-w-[20px] text-center leading-none">
                            {selectedInCategory}
                          </Badge>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => selectAllInCategory(category.id)}
                          className={`h-7 px-1.5 sm:px-2 text-xs min-w-[40px] sm:min-w-[50px] whitespace-nowrap text-center ${getNetZeroCategoryTheme(category.name).borderColor} ${getNetZeroCategoryTheme(category.name).textColor} hover:bg-white`}
                        >
                          {allInCategorySelected ? 'None' : 'All'}
                        </Button>
                      </div>
                    </div>
                    
                    <CollapsibleContent className="space-y-1">
                      <div className="ml-4 sm:ml-6 space-y-1 border-l-2 border-muted pl-2 sm:pl-4">
                        {category.subcategories.map((subcategory) => (
                          <div key={subcategory.id} className="flex items-center space-x-2 sm:space-x-3 p-2 sm:p-3 rounded hover:bg-muted/30 min-h-[44px] sm:min-h-[48px] touch-manipulation overflow-hidden">
                            <Checkbox
                              id={`netzero-${subcategory.id}`}
                              checked={filters.selectedNetZeroCategories.includes(subcategory.id)}
                              onCheckedChange={() => toggleNetZeroCategory(subcategory.id)}
                              className="shrink-0 h-4 w-4"
                            />
                            <Label
                              htmlFor={`netzero-${subcategory.id}`}
                              className="text-xs sm:text-sm cursor-pointer flex-1 leading-relaxed select-none min-w-0 break-words"
                            >
                              {subcategory.name}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                  {index < netZeroCategories.length - 1 && (
                    <div className="mx-2 border-b border-muted/50"></div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Business Directory</h1>
        <p className="text-muted-foreground">
          Browse sustainable businesses in our network with their headquarters locations.
        </p>
      </div>

      {/* Net Zero Category Cards */}
      <NetZeroCategoryCards
        onCategorySelect={handleCategorySelect}
        selectedCategoryId={filters.selectedCategoryId}
      />

      {/* Search and Filter Controls */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
            <CardTitle className="flex items-center gap-2">
              <Search className="w-5 h-5" />
              Search & Filter
            </CardTitle>
            <div className="flex items-center gap-2 flex-wrap">
              {hasActiveFilters() && (
                <Badge variant="secondary" className="flex items-center gap-1 text-xs">
                  {filteredAndSortedBusinesses.length} of {businesses.length} businesses
                </Badge>
              )}

              {/* Verified Only Quick Filter */}
              <div className="flex items-center space-x-2 px-3 py-2 border rounded-md bg-background">
                <Checkbox
                  id="header-verified-only"
                  checked={filters.verifiedOnly}
                  onCheckedChange={(checked) => setFilters(prev => ({ ...prev, verifiedOnly: !!checked }))}
                />
                <Label htmlFor="header-verified-only" className="text-sm cursor-pointer flex items-center gap-1">
                  <Shield className="w-3 h-3 text-blue-600" />
                  <span className="hidden sm:inline">Verified Only</span>
                  <span className="sm:hidden">Verified</span>
                </Label>
              </div>

              {/* Sort Control */}
              <div className="flex items-center gap-2">
                <ArrowUpDown className="w-4 h-4 text-muted-foreground" />
                <Select value={filters.sortBy} onValueChange={(value: BusinessSortOption) => setFilters(prev => ({ ...prev, sortBy: value }))}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name-asc">Name A-Z</SelectItem>
                    <SelectItem value="name-desc">Name Z-A</SelectItem>
                    <SelectItem value="joined-desc">Newest First</SelectItem>
                    <SelectItem value="joined-asc">Oldest First</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button
                variant={filters.showFilters ? "default" : "outline"}
                size="sm"
                onClick={() => toggleFilter('showFilters')}
                className={`${filters.showFilters ? "bg-blue-600 hover:bg-blue-700" : ""} min-w-[120px]`}
              >
                <Filter className="w-4 h-4 mr-2" />
                <span className="hidden xs:inline">{filters.showFilters ? 'Hide' : 'Show'} </span>Filters
                {(filters.verifiedOnly || filters.selectedMainIndustries.length > 0 || filters.selectedTargetIndustries.length > 0 || filters.selectedNetZeroCategories.length > 0) && (
                  <Badge variant="secondary" className="ml-2 bg-white text-blue-600 text-xs">
                    {(filters.verifiedOnly ? 1 : 0) + filters.selectedMainIndustries.length + filters.selectedTargetIndustries.length + filters.selectedNetZeroCategories.length}
                  </Badge>
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Main Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search businesses..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-10 h-11"
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-9 w-9 p-0"
                onClick={() => setSearchTerm('')}
                aria-label="Clear search"
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>

          {/* Advanced Filters */}
          {filters.showFilters && (
            <div className="pt-4 border-t space-y-6 overflow-hidden">
              {/* Industry Filters Row */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 min-w-0">
                {/* Main Industry Filter */}
                {!loadingIndustries && (
                  <Card className="p-3 lg:p-4 min-w-0">
                    {renderIndustryFilter(
                      "Business Industry",
                      filters.selectedMainIndustries,
                      toggleMainIndustry
                    )}
                  </Card>
                )}
                
                {loadingIndustries && (
                  <Card className="p-3 lg:p-4">
                    <div className="flex items-center justify-center py-8">
                      <div className="text-sm text-muted-foreground">Loading industries...</div>
                    </div>
                  </Card>
                )}

                {/* Target Industries Filter */}
                {!loadingIndustries && (
                  <Card className="p-3 lg:p-4 min-w-0">
                    {renderIndustryFilter(
                      "Relevant Industries",
                      filters.selectedTargetIndustries,
                      toggleTargetIndustry
                    )}
                  </Card>
                )}
                
                {loadingIndustries && (
                  <Card className="p-3 lg:p-4">
                    <div className="flex items-center justify-center py-8">
                      <div className="text-sm text-muted-foreground">Loading industries...</div>
                    </div>
                  </Card>
                )}
              </div>

              {/* Net-Zero Categories Filter Row */}
              {!loadingNetZeroCategories && netZeroCategories.length > 0 && (
                <Card className="p-3 lg:p-4 min-w-0">
                  {renderNetZeroCategoriesFilter()}
                </Card>
              )}
              
              {loadingNetZeroCategories && (
                <Card className="p-3 lg:p-4">
                  <div className="flex items-center justify-center py-8">
                    <div className="text-sm text-muted-foreground">Loading net-zero categories...</div>
                  </div>
                </Card>
              )}

              {/* Clear All Filters Button */}
              <div className="flex justify-center pt-4 border-t">
                <Button 
                  variant="outline" 
                  onClick={clearAllFilters}
                  disabled={!hasActiveFilters()}
                  className="w-full sm:w-auto sm:min-w-[200px]"
                >
                  <X className="w-4 h-4 mr-2" />
                  Clear All Filters
                </Button>
              </div>
            </div>
          )}

          {/* Active Filters Display */}
          {hasActiveFilters() && (
            <div className="space-y-3 pt-3 border-t bg-muted/20 rounded-md p-3">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                <span className="text-sm font-medium text-muted-foreground">Active Filters:</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="h-8 px-3 text-xs text-muted-foreground hover:text-foreground self-start sm:self-auto"
                >
                  Clear All
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {searchTerm && (
                  <Badge variant="secondary" className="flex items-center gap-1 bg-muted/50 hover:bg-muted text-sm max-w-full min-h-[32px]">
                    <Search className="w-3 h-3 flex-shrink-0" />
                    <span className="truncate">"{searchTerm.length > 12 ? searchTerm.substring(0, 12) + '...' : searchTerm}"</span>
                    <X 
                      className="w-3 h-3 cursor-pointer hover:bg-muted-foreground/20 rounded-full p-0.5 flex-shrink-0 touch-manipulation" 
                      onClick={() => setSearchTerm('')}
                    />
                  </Badge>
                )}
                {filters.verifiedOnly && (
                  <Badge variant="secondary" className="flex items-center gap-1 bg-muted/50 hover:bg-muted text-sm min-h-[32px]">
                    <Shield className="w-3 h-3 flex-shrink-0" />
                    <span>Verified Only</span>
                    <X 
                      className="w-3 h-3 cursor-pointer hover:bg-muted-foreground/20 rounded-full p-0.5 flex-shrink-0 touch-manipulation" 
                      onClick={() => toggleFilter('verifiedOnly')}
                    />
                  </Badge>
                )}
                {filters.selectedMainIndustries.map(industryId => {
                  const industry = industries.flatMap(p => p.children).find(c => c.id === industryId);
                  const parent = industries.find(p => p.children.some(c => c.id === industryId));
                  const theme = parent ? getIndustryTheme(parent.name) : null;
                  return industry ? (
                    <Badge key={industryId} variant="secondary" className={`flex items-center gap-1 text-sm max-w-full min-h-[32px] border ${theme ? `border-gray-300 ${theme.textColor}` : 'border-gray-300 bg-muted/50 hover:bg-muted'}`}>
                      {theme ? (
                        <IndustryIcon industryName={parent!.name} size="sm" className="w-3 h-3 flex-shrink-0" />
                      ) : (
                        <Building2 className="w-3 h-3 flex-shrink-0" />
                      )}
                      <span className="truncate">Business: {industry.name.length > 15 ? industry.name.substring(0, 15) + '...' : industry.name}</span>
                      <X 
                        className="w-3 h-3 cursor-pointer hover:bg-muted-foreground/20 rounded-full p-0.5 flex-shrink-0 touch-manipulation" 
                        onClick={() => toggleMainIndustry(industryId)}
                      />
                    </Badge>
                  ) : null;
                })}
                {filters.selectedTargetIndustries.map(industryId => {
                  const industry = industries.flatMap(p => p.children).find(c => c.id === industryId);
                  const parent = industries.find(p => p.children.some(c => c.id === industryId));
                  const theme = parent ? getIndustryTheme(parent.name) : null;
                  return industry ? (
                    <Badge key={industryId} variant="secondary" className={`flex items-center gap-1 text-sm max-w-full min-h-[32px] border ${theme ? `border-gray-300 ${theme.textColor}` : 'border-gray-300 bg-muted/50 hover:bg-muted'}`}>
                      {theme ? (
                        <IndustryIcon industryName={parent!.name} size="sm" className="w-3 h-3 flex-shrink-0" />
                      ) : (
                        <Building2 className="w-3 h-3 flex-shrink-0" />
                      )}
                      <span className="truncate">Relevant: {industry.name.length > 15 ? industry.name.substring(0, 15) + '...' : industry.name}</span>
                      <X 
                        className="w-3 h-3 cursor-pointer hover:bg-muted-foreground/20 rounded-full p-0.5 flex-shrink-0 touch-manipulation" 
                        onClick={() => toggleTargetIndustry(industryId)}
                      />
                    </Badge>
                  ) : null;
                })}
                {filters.selectedNetZeroCategories.map(subcategoryId => {
                  const subcategory = netZeroCategories.flatMap(c => c.subcategories).find(s => s.id === subcategoryId);
                  const category = netZeroCategories.find(c => c.subcategories.some(s => s.id === subcategoryId));
                  const theme = category ? getNetZeroCategoryTheme(category.name) : null;
                  return subcategory ? (
                    <Badge key={subcategoryId} variant="secondary" className={`flex items-center gap-1 text-sm max-w-full min-h-[32px] border ${theme ? `border-gray-300 ${theme.textColor}` : 'border-gray-300 text-emerald-700'}`}>
                      {theme ? (
                        <NetZeroCategoryIcon categoryName={category!.name} size="sm" className="w-3 h-3 flex-shrink-0" />
                      ) : (
                        <span className="w-2 h-2 bg-emerald-500 rounded-full flex-shrink-0"></span>
                      )}
                      <span className="truncate">Net-Zero: {subcategory.name.length > 15 ? subcategory.name.substring(0, 15) + '...' : subcategory.name}</span>
                      <X 
                        className="w-3 h-3 cursor-pointer hover:bg-muted-foreground/20 rounded-full p-0.5 flex-shrink-0 touch-manipulation" 
                        onClick={() => toggleNetZeroCategory(subcategoryId)}
                      />
                    </Badge>
                  ) : null;
                })}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      {loading && (
        <div className="text-center py-8">
          <p>Loading businesses...</p>
        </div>
      )}

      {error && (
        <div className="p-4 border border-red-200 bg-red-50 text-red-700 rounded-lg mb-6">
          <p>{error}</p>
        </div>
      )}

      {!loading && !error && filteredAndSortedBusinesses.length === 0 && (
        <div className="p-8 border border-dashed rounded-lg text-center">
          <Search className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
          <p className="text-lg">
            {hasActiveFilters() ? 'No businesses match your filters' : 'No businesses found'}
          </p>
          <p className="text-muted-foreground mt-2">
            {hasActiveFilters() ? 'Try adjusting your search terms or filters.' : 'Be the first to add your sustainable business to our directory.'}
          </p>
        </div>
      )}

      {!loading && !error && filteredAndSortedBusinesses.length > 0 && (
        <>
          <div className="mb-4">
            <p className="text-sm text-muted-foreground">
              {hasActiveFilters() 
                ? `Showing ${filteredAndSortedBusinesses.length} of ${businesses.length} businesses`
                : `${businesses.length} businesses`
              }
            </p>
          </div>
          
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredAndSortedBusinesses.map((business) => (
              <Card 
                key={business.id} 
                className="cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => navigate(`/business/${business.id}`)}
              >
                <CardHeader className="pb-2">
                  {/* Business name at the top */}
                  <div className="flex items-center justify-between mb-2">
                    <CardTitle className="text-lg leading-tight">
                      {business.business_name}
                    </CardTitle>
                    {business.is_verified && (
                      <Badge variant="default" className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1 flex items-center gap-1">
                        <Shield className="w-3 h-3" />
                        Verified
                      </Badge>
                    )}
                  </div>
                  
                  {/* Visual divider after business name */}
                  <div className="w-full h-px bg-border mb-4"></div>
                  
                  {/* Content area with image on the right */}
                  <div className="flex items-start gap-4">
                    <div className="flex-1 min-w-0 space-y-2.5">
                      {/* Headquarters Location - always show with icon for consistent alignment */}
                      <div className="flex items-start gap-2">
                        <Building2 className="w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <div className="min-w-0">
                          <div className="text-sm font-medium">Headquarters</div>
                          {(business as any).headquarters_location?.headquarters_name ? (
                            <div className="text-sm font-medium text-muted-foreground truncate">
                              {(business as any).headquarters_location.headquarters_name}
                            </div>
                          ) : business.headquarters_location_id ? (
                            <div className="text-xs text-muted-foreground font-mono truncate">
                              ID: {business.headquarters_location_id}
                            </div>
                          ) : (
                            <div className="text-sm text-muted-foreground italic">
                              Not specified
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Website - always show with icon for consistent alignment */}
                      <div className="flex items-center gap-2 text-sm">
                        <Globe className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                        <div className="min-w-0 flex-1">
                          {business.website ? (
                            <a 
                              href={business.website} 
                              target="_blank" 
                              rel="noopener noreferrer" 
                              className="text-primary hover:underline truncate block"
                              onClick={(e) => e.stopPropagation()}
                            >
                              {business.website.replace(/^https?:\/\//, '')}
                            </a>
                          ) : (
                            <span className="text-muted-foreground italic">No website</span>
                          )}
                        </div>
                      </div>

                      {/* Main Industry */}
                      {business.mainIndustry && (
                        <div className="mt-2">
                          <span className="text-sm text-muted-foreground mr-2">Main Industry:</span>
                          {(() => {
                            const theme = business.mainIndustry.parent ? getIndustryTheme(business.mainIndustry.parent.name) : null;
                            return (
                              <Badge variant="outline" className={`border-2 ${theme ? `${theme.borderColor} ${theme.textColor}` : "border-purple-600 text-purple-600"}`}>
                                {theme && (
                                  <IndustryIcon industryName={business.mainIndustry.parent!.name} size="sm" className="w-3 h-3 mr-1" />
                                )}
                                {business.mainIndustry.name}
                              </Badge>
                            );
                          })()}
                        </div>
                      )}

                      {/* Net-Zero Focus Area (Primary Only) */}
                      {business.primaryNetZeroCategory && (
                        <div className="mt-2">
                          <span className="text-sm text-muted-foreground mr-2">Main Net Zero Area:</span>
                          {(() => {
                            const theme = business.primaryNetZeroCategory.category ? getNetZeroCategoryTheme(business.primaryNetZeroCategory.category.name) : null;
                            return (
                              <Badge variant="outline" className={`border-2 ${theme ? `${theme.borderColor} ${theme.textColor}` : "border-green-600 text-green-600"}`}>
                                {theme && (
                                  <NetZeroCategoryIcon categoryName={business.primaryNetZeroCategory.category!.name} size="sm" className="w-3 h-3 mr-1" />
                                )}
                                {business.primaryNetZeroCategory.name}
                              </Badge>
                            );
                          })()}
                        </div>
                      )}
                    </div>
                    
                    {/* Larger business image on the right (1.25x bigger: 100px) */}
                    <div className="flex-shrink-0">
                      <BusinessLogoDisplay
                        logoUrl={business.logo_url}
                        businessName={business.business_name}
                        size="xl"
                        shape="square"
                        className="w-[100px] h-[100px]"
                      />
                    </div>
                  </div>
                </CardHeader>
                
                {/* Visual divider before date */}
                <div className="px-6">
                  <div className="w-full h-px bg-border"></div>
                </div>
                
                <CardContent className="pt-2 pb-4">
                  <div className="flex items-center gap-1.5 text-muted-foreground text-xs">
                    <Calendar className="w-3 h-3" />
                    Added {new Date(business.created_at || '').toLocaleDateString()}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default BusinessDirectoryPage;
