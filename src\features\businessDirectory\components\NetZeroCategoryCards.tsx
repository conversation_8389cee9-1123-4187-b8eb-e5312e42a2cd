import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Building2, Leaf } from 'lucide-react';
import { NetZeroCategoryService } from '@/services/netZeroCategoryService';
import { getNetZeroCategoryTheme, NetZeroCategoryIcon } from '@/utils/netZeroCategoryIcons';
import type { CategoryBusinessCount } from '@/types/netzero-categories.types';

interface NetZeroCategoryCardsProps {
  onCategorySelect: (categoryId: string, categoryName: string) => void;
  selectedCategoryId?: string;
}

const NetZeroCategoryCards: React.FC<NetZeroCategoryCardsProps> = ({
  onCategorySelect,
  selectedCategoryId
}) => {
  const [categories, setCategories] = useState<CategoryBusinessCount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategoryBusinessCounts = async () => {
      try {
        setLoading(true);
        setError(null);
        const counts = await NetZeroCategoryService.getCategoryBusinessCounts();
        setCategories(counts);
      } catch (err) {
        console.error('Failed to fetch category business counts:', err);
        setError('Failed to load category data');
      } finally {
        setLoading(false);
      }
    };

    fetchCategoryBusinessCounts();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">Loading categories...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-2">{error}</p>
        <button 
          onClick={() => window.location.reload()} 
          className="text-blue-600 hover:underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="mb-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2 flex items-center gap-2">
          <Leaf className="w-6 h-6 text-green-600" />
          Browse by Net Zero Category
        </h2>
        <p className="text-muted-foreground">
          Discover businesses by their sustainability focus area. Click a category to filter the directory.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {categories.map((category) => {
          const theme = getNetZeroCategoryTheme(category.categoryName);
          const isSelected = selectedCategoryId === category.categoryId;
          
          return (
            <Card
              key={category.categoryId}
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 border-2 ${
                isSelected
                  ? `${theme.borderColor} ${theme.hoverBgColor}`
                  : `border-gray-200 hover:${theme.borderColor} ${theme.hoverBgColor}`
              }`}
              onClick={() => onCategorySelect(category.categoryId, category.categoryName)}
            >
              <CardHeader className="pb-3">
                <CardTitle className="text-lg leading-tight flex items-start justify-between">
                  <div className="flex items-center gap-2 flex-1 pr-2">
                    <NetZeroCategoryIcon 
                      categoryName={category.categoryName} 
                      size="lg" 
                      className={isSelected ? theme.textColor : theme.textColor}
                    />
                    <span className={`${isSelected ? `${theme.textColor} font-semibold` : theme.textColor}`}>
                      {category.categoryName}
                    </span>
                  </div>
                  <Badge 
                    variant={isSelected ? "default" : "secondary"}
                    className={`flex items-center gap-1 shrink-0 border ${
                      isSelected 
                        ? `${theme.borderColor} ${theme.textColor}` 
                        : `border-gray-300 ${theme.textColor}`
                    }`}
                  >
                    <Building2 className="w-3 h-3" />
                    {category.businessCount}
                  </Badge>
                </CardTitle>
              </CardHeader>
              {category.description && (
                <CardContent className="pt-0">
                  <p className={`text-sm line-clamp-3 ${isSelected ? theme.textColor : 'text-muted-foreground'}`}>
                    {category.description}
                  </p>
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>

      {selectedCategoryId && (
        <div className="mt-4 flex items-center justify-center">
          <button
            onClick={() => onCategorySelect('', '')}
            className="text-sm text-blue-600 hover:underline flex items-center gap-1"
          >
            Clear category filter
          </button>
        </div>
      )}
    </div>
  );
};

export default NetZeroCategoryCards;
