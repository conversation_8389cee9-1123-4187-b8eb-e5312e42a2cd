-- Create User Tagging/Mention System for Social Feed
-- This migration creates tables and functions for user mentions in posts and comments

-- Add mention preference to profiles table
ALTER TABLE public.profiles 
ADD COLUMN allow_mentions boolean DEFAULT true;

-- Create social post mentions table
CREATE TABLE IF NOT EXISTS public.social_post_mentions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES public.social_posts(id) ON DELETE CASCADE,
    mentioned_user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    mentioned_by_user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    mention_text TEXT NOT NULL, -- The actual @username text used
    position_start INTEGER NOT NULL, -- Start position in the content
    position_end INTEGER NOT NULL, -- End position in the content
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(post_id, mentioned_user_id, position_start), -- Prevent duplicate mentions at same position
    CHECK (position_start >= 0),
    CHECK (position_end > position_start),
    CHEC<PERSON> (char_length(mention_text) >= 2 AND char_length(mention_text) <= 100)
);

-- Create social comment mentions table
CREATE TABLE IF NOT EXISTS public.social_comment_mentions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    comment_id UUID NOT NULL REFERENCES public.social_comments(id) ON DELETE CASCADE,
    mentioned_user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    mentioned_by_user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    mention_text TEXT NOT NULL, -- The actual @username text used
    position_start INTEGER NOT NULL, -- Start position in the content
    position_end INTEGER NOT NULL, -- End position in the content
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(comment_id, mentioned_user_id, position_start), -- Prevent duplicate mentions at same position
    CHECK (position_start >= 0),
    CHECK (position_end > position_start),
    CHECK (char_length(mention_text) >= 2 AND char_length(mention_text) <= 100)
);

-- Create indexes for performance
CREATE INDEX idx_social_post_mentions_post_id ON public.social_post_mentions(post_id);
CREATE INDEX idx_social_post_mentions_mentioned_user_id ON public.social_post_mentions(mentioned_user_id);
CREATE INDEX idx_social_post_mentions_mentioned_by_user_id ON public.social_post_mentions(mentioned_by_user_id);

CREATE INDEX idx_social_comment_mentions_comment_id ON public.social_comment_mentions(comment_id);
CREATE INDEX idx_social_comment_mentions_mentioned_user_id ON public.social_comment_mentions(mentioned_user_id);
CREATE INDEX idx_social_comment_mentions_mentioned_by_user_id ON public.social_comment_mentions(mentioned_by_user_id);

-- Function to search users for mentions (returns users that allow mentions)
CREATE OR REPLACE FUNCTION search_mentionable_users(search_query TEXT, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
    id UUID,
    first_name TEXT,
    last_name TEXT,
    job_title TEXT,
    organisation_name TEXT,
    avatar_url TEXT,
    display_name TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.first_name,
        p.last_name,
        p.job_title,
        p.organisation_name,
        p.avatar_url,
        CASE 
            WHEN p.first_name IS NOT NULL AND p.last_name IS NOT NULL THEN 
                p.first_name || ' ' || p.last_name
            WHEN p.first_name IS NOT NULL THEN 
                p.first_name
            WHEN p.last_name IS NOT NULL THEN 
                p.last_name
            ELSE 
                'Anonymous User'
        END as display_name
    FROM public.profiles p
    WHERE 
        p.profile_visible = true 
        AND p.allow_mentions = true
        AND (
            p.first_name ILIKE '%' || search_query || '%' 
            OR p.last_name ILIKE '%' || search_query || '%'
            OR (p.first_name || ' ' || p.last_name) ILIKE '%' || search_query || '%'
            OR p.organisation_name ILIKE '%' || search_query || '%'
        )
    ORDER BY 
        -- Prioritize exact matches
        CASE 
            WHEN (p.first_name || ' ' || p.last_name) ILIKE search_query || '%' THEN 1
            WHEN p.first_name ILIKE search_query || '%' THEN 2
            WHEN p.last_name ILIKE search_query || '%' THEN 3
            ELSE 4
        END,
        p.first_name, p.last_name
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create mention notifications for posts
CREATE OR REPLACE FUNCTION notify_mentioned_users_in_post()
RETURNS TRIGGER AS $$
DECLARE
    mentioned_user_record RECORD;
    post_record RECORD;
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
BEGIN
    -- Get post details
    SELECT p.*, 
           author.first_name as author_first_name,
           author.last_name as author_last_name
    INTO post_record
    FROM public.social_posts p
    JOIN public.profiles author ON p.user_id = author.id
    WHERE p.id = NEW.post_id;
    
    -- Get mentioned user details
    SELECT * INTO mentioned_user_record
    FROM public.profiles
    WHERE id = NEW.mentioned_user_id;
    
    -- Only create notification if user allows mentions and is not mentioning themselves
    IF mentioned_user_record.allow_mentions = true AND NEW.mentioned_user_id != NEW.mentioned_by_user_id THEN
        -- Create notification title and message
        notification_title := COALESCE(post_record.author_first_name, 'Someone') || ' mentioned you in a post';
        notification_message := 'You were mentioned in a social post: "' || 
                               LEFT(post_record.content, 100) || 
                               CASE WHEN LENGTH(post_record.content) > 100 THEN '...' ELSE '' END || '"';
        
        -- Create action URL to the post
        action_url := '/social/post/' || NEW.post_id::TEXT;
        
        -- Create notification data
        notification_data := jsonb_build_object(
            'post_id', NEW.post_id,
            'mentioned_by_user_id', NEW.mentioned_by_user_id,
            'mention_text', NEW.mention_text,
            'post_content_preview', LEFT(post_record.content, 200)
        );
        
        -- Create notification
        PERFORM create_notification(
            NEW.mentioned_user_id,
            'social_mention'::notification_type_enum,
            notification_title,
            notification_message,
            NEW.post_id,
            'social_post',
            action_url,
            notification_data
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create mention notifications for comments
CREATE OR REPLACE FUNCTION notify_mentioned_users_in_comment()
RETURNS TRIGGER AS $$
DECLARE
    mentioned_user_record RECORD;
    comment_record RECORD;
    post_record RECORD;
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
BEGIN
    -- Get comment and post details
    SELECT c.*, 
           author.first_name as author_first_name,
           author.last_name as author_last_name,
           p.content as post_content
    INTO comment_record
    FROM public.social_comments c
    JOIN public.profiles author ON c.user_id = author.id
    JOIN public.social_posts p ON c.post_id = p.id
    WHERE c.id = NEW.comment_id;
    
    -- Get mentioned user details
    SELECT * INTO mentioned_user_record
    FROM public.profiles
    WHERE id = NEW.mentioned_user_id;
    
    -- Only create notification if user allows mentions and is not mentioning themselves
    IF mentioned_user_record.allow_mentions = true AND NEW.mentioned_user_id != NEW.mentioned_by_user_id THEN
        -- Create notification title and message
        notification_title := COALESCE(comment_record.author_first_name, 'Someone') || ' mentioned you in a comment';
        notification_message := 'You were mentioned in a comment: "' || 
                               LEFT(comment_record.content, 100) || 
                               CASE WHEN LENGTH(comment_record.content) > 100 THEN '...' ELSE '' END || '"';
        
        -- Create action URL to the post (will scroll to comment)
        action_url := '/social/post/' || comment_record.post_id::TEXT || '#comment-' || NEW.comment_id::TEXT;
        
        -- Create notification data
        notification_data := jsonb_build_object(
            'comment_id', NEW.comment_id,
            'post_id', comment_record.post_id,
            'mentioned_by_user_id', NEW.mentioned_by_user_id,
            'mention_text', NEW.mention_text,
            'comment_content_preview', LEFT(comment_record.content, 200),
            'post_content_preview', LEFT(comment_record.post_content, 200)
        );
        
        -- Create notification
        PERFORM create_notification(
            NEW.mentioned_user_id,
            'social_mention'::notification_type_enum,
            notification_title,
            notification_message,
            comment_record.post_id,
            'social_comment',
            action_url,
            notification_data
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for mention notifications
CREATE TRIGGER trigger_notify_post_mentions
    AFTER INSERT ON public.social_post_mentions
    FOR EACH ROW
    EXECUTE FUNCTION notify_mentioned_users_in_post();

CREATE TRIGGER trigger_notify_comment_mentions
    AFTER INSERT ON public.social_comment_mentions
    FOR EACH ROW
    EXECUTE FUNCTION notify_mentioned_users_in_comment();

-- Grant permissions on new tables
GRANT ALL ON TABLE public.social_post_mentions TO authenticated;
GRANT ALL ON TABLE public.social_comment_mentions TO authenticated;
GRANT ALL ON TABLE public.social_post_mentions TO service_role;
GRANT ALL ON TABLE public.social_comment_mentions TO service_role;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION search_mentionable_users(TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION search_mentionable_users(TEXT, INTEGER) TO service_role;

-- Add comments for documentation
COMMENT ON TABLE public.social_post_mentions IS 'User mentions/tags in social posts with position tracking';
COMMENT ON TABLE public.social_comment_mentions IS 'User mentions/tags in social comments with position tracking';
COMMENT ON COLUMN public.profiles.allow_mentions IS 'Whether user allows being mentioned/tagged by others';
COMMENT ON FUNCTION search_mentionable_users(TEXT, INTEGER) IS 'Search for users that can be mentioned, filtered by mention preferences';
