import { supabase } from '@/integrations/supabase/client';
import { UserSearchService, type MentionableUser } from './userSearchService';
import { MentionParser, type ParsedMention, type MentionData } from '../utils/mentionParser';

// Interface for mention records from database
export interface PostMentionRecord {
  id: string;
  post_id: string;
  mentioned_user_id: string;
  mentioned_by_user_id: string;
  mention_text: string;
  position_start: number;
  position_end: number;
  created_at: string;
}

export interface CommentMentionRecord {
  id: string;
  comment_id: string;
  mentioned_user_id: string;
  mentioned_by_user_id: string;
  mention_text: string;
  position_start: number;
  position_end: number;
  created_at: string;
}

export class MentionService {
  /**
   * Process mentions in content and return validated mentions
   */
  static async processMentions(content: string): Promise<{
    mentions: ParsedMention[];
    validatedUsers: MentionableUser[];
  }> {
    console.log('Processing mentions in content:', content);

    // Find potential mentions in content
    const mentionMatches = MentionParser.findMentionMatches(content);
    console.log('Found mention matches:', mentionMatches);

    if (mentionMatches.length === 0) {
      console.log('No mention matches found');
      return { mentions: [], validatedUsers: [] };
    }

    // Extract unique user queries from mentions
    const userQueries = mentionMatches.map(match =>
      match.text.substring(1).trim() // Remove @ and trim
    );
    console.log('User queries from mentions:', userQueries);

    // Search for users that match the mention queries
    const allUsers: MentionableUser[] = [];
    for (const query of userQueries) {
      if (query.length > 0) {
        try {
          const users = await UserSearchService.searchMentionableUsers(query, 20);
          console.log(`Found ${users.length} users for query "${query}":`, users);
          allUsers.push(...users);
        } catch (error) {
          console.error('Error searching users for mention:', error);
        }
      }
    }

    // Remove duplicates
    const uniqueUsers = allUsers.filter((user, index, self) => 
      index === self.findIndex(u => u.id === user.id)
    );

    // Parse mentions against found users
    const parsedMentions = MentionParser.parseMentions(content, uniqueUsers);
    console.log('Parsed mentions:', parsedMentions);
    console.log('Validated users:', uniqueUsers);

    return {
      mentions: parsedMentions,
      validatedUsers: uniqueUsers
    };
  }

  /**
   * Save post mentions to database
   */
  static async savePostMentions(
    postId: string,
    mentions: ParsedMention[]
  ): Promise<PostMentionRecord[]> {
    console.log('Saving post mentions:', { postId, mentions });

    if (mentions.length === 0) {
      console.log('No mentions to save');
      return [];
    }

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User must be authenticated to save mentions');
    }

    const mentionData = mentions.map(mention => ({
      post_id: postId,
      mentioned_user_id: mention.userId,
      mentioned_by_user_id: user.id,
      mention_text: mention.mentionText,
      position_start: mention.startPosition,
      position_end: mention.endPosition
    }));

    console.log('Inserting mention data:', mentionData);

    const { data, error } = await supabase
      .from('social_post_mentions')
      .insert(mentionData)
      .select();

    if (error) {
      console.error('Error saving post mentions:', error);
      throw new Error(`Failed to save post mentions: ${error.message}`);
    }

    console.log('Successfully saved mentions:', data);
    return data || [];
  }

  /**
   * Save comment mentions to database
   */
  static async saveCommentMentions(
    commentId: string,
    mentions: ParsedMention[]
  ): Promise<CommentMentionRecord[]> {
    if (mentions.length === 0) {
      return [];
    }

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User must be authenticated to save mentions');
    }

    const mentionData = mentions.map(mention => ({
      comment_id: commentId,
      mentioned_user_id: mention.userId,
      mentioned_by_user_id: user.id,
      mention_text: mention.mentionText,
      position_start: mention.startPosition,
      position_end: mention.endPosition
    }));

    const { data, error } = await supabase
      .from('social_comment_mentions')
      .insert(mentionData)
      .select();

    if (error) {
      throw new Error(`Failed to save comment mentions: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get mentions for a post
   */
  static async getPostMentions(postId: string): Promise<PostMentionRecord[]> {
    const { data, error } = await supabase
      .from('social_post_mentions')
      .select(`
        *,
        mentioned_user:profiles!social_post_mentions_mentioned_user_id_fkey (
          id,
          first_name,
          last_name,
          job_title,
          organisation_name,
          avatar_url,
          profile_visible
        )
      `)
      .eq('post_id', postId)
      .order('position_start');

    if (error) {
      throw new Error(`Failed to get post mentions: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get mentions for a comment
   */
  static async getCommentMentions(commentId: string): Promise<CommentMentionRecord[]> {
    const { data, error } = await supabase
      .from('social_comment_mentions')
      .select(`
        *,
        mentioned_user:profiles!social_comment_mentions_mentioned_user_id_fkey (
          id,
          first_name,
          last_name,
          job_title,
          organisation_name,
          avatar_url,
          profile_visible
        )
      `)
      .eq('comment_id', commentId)
      .order('position_start');

    if (error) {
      throw new Error(`Failed to get comment mentions: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get mentions for multiple posts (for feed display)
   */
  static async getPostsMentions(postIds: string[]): Promise<Record<string, PostMentionRecord[]>> {
    if (postIds.length === 0) {
      return {};
    }

    const { data, error } = await supabase
      .from('social_post_mentions')
      .select(`
        *,
        mentioned_user:profiles!social_post_mentions_mentioned_user_id_fkey (
          id,
          first_name,
          last_name,
          job_title,
          organisation_name,
          avatar_url,
          profile_visible
        )
      `)
      .in('post_id', postIds)
      .order('position_start');

    if (error) {
      throw new Error(`Failed to get posts mentions: ${error.message}`);
    }

    // Group by post_id
    const mentionsByPost: Record<string, PostMentionRecord[]> = {};
    for (const mention of data || []) {
      if (!mentionsByPost[mention.post_id]) {
        mentionsByPost[mention.post_id] = [];
      }
      mentionsByPost[mention.post_id].push(mention);
    }

    return mentionsByPost;
  }

  /**
   * Get mentions for multiple comments (for comment display)
   */
  static async getCommentsMentions(commentIds: string[]): Promise<Record<string, CommentMentionRecord[]>> {
    if (commentIds.length === 0) {
      return {};
    }

    const { data, error } = await supabase
      .from('social_comment_mentions')
      .select(`
        *,
        mentioned_user:profiles!social_comment_mentions_mentioned_user_id_fkey (
          id,
          first_name,
          last_name,
          job_title,
          organisation_name,
          avatar_url,
          profile_visible
        )
      `)
      .in('comment_id', commentIds)
      .order('position_start');

    if (error) {
      throw new Error(`Failed to get comments mentions: ${error.message}`);
    }

    // Group by comment_id
    const mentionsByComment: Record<string, CommentMentionRecord[]> = {};
    for (const mention of data || []) {
      if (!mentionsByComment[mention.comment_id]) {
        mentionsByComment[mention.comment_id] = [];
      }
      mentionsByComment[mention.comment_id].push(mention);
    }

    return mentionsByComment;
  }

  /**
   * Delete mentions for a post (when post is deleted)
   */
  static async deletePostMentions(postId: string): Promise<void> {
    const { error } = await supabase
      .from('social_post_mentions')
      .delete()
      .eq('post_id', postId);

    if (error) {
      throw new Error(`Failed to delete post mentions: ${error.message}`);
    }
  }

  /**
   * Delete mentions for a comment (when comment is deleted)
   */
  static async deleteCommentMentions(commentId: string): Promise<void> {
    const { error } = await supabase
      .from('social_comment_mentions')
      .delete()
      .eq('comment_id', commentId);

    if (error) {
      throw new Error(`Failed to delete comment mentions: ${error.message}`);
    }
  }

  /**
   * Get mentions where user is mentioned (for notifications/profile)
   */
  static async getUserMentions(userId: string, limit: number = 20): Promise<{
    postMentions: PostMentionRecord[];
    commentMentions: CommentMentionRecord[];
  }> {
    const [postMentionsResult, commentMentionsResult] = await Promise.all([
      supabase
        .from('social_post_mentions')
        .select(`
          *,
          post:social_posts!social_post_mentions_post_id_fkey (
            id,
            content,
            created_at,
            user_id
          ),
          mentioned_by:profiles!social_post_mentions_mentioned_by_user_id_fkey (
            id,
            first_name,
            last_name,
            avatar_url
          )
        `)
        .eq('mentioned_user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit),
      
      supabase
        .from('social_comment_mentions')
        .select(`
          *,
          comment:social_comments!social_comment_mentions_comment_id_fkey (
            id,
            content,
            created_at,
            user_id,
            post_id
          ),
          mentioned_by:profiles!social_comment_mentions_mentioned_by_user_id_fkey (
            id,
            first_name,
            last_name,
            avatar_url
          )
        `)
        .eq('mentioned_user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit)
    ]);

    if (postMentionsResult.error) {
      throw new Error(`Failed to get user post mentions: ${postMentionsResult.error.message}`);
    }

    if (commentMentionsResult.error) {
      throw new Error(`Failed to get user comment mentions: ${commentMentionsResult.error.message}`);
    }

    return {
      postMentions: postMentionsResult.data || [],
      commentMentions: commentMentionsResult.data || []
    };
  }
}
