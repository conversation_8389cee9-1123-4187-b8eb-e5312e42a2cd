-- Fix mention notification content to remove UUID format
-- This migration properly cleans UUID format from notification content

-- Update the post mention notification function with proper UUID cleaning
CREATE OR REPLACE FUNCTION notify_mentioned_users_in_post()
RETURNS TRIGGER AS $$
DECLARE
    mentioned_user_record RECORD;
    post_record RECORD;
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
    clean_content TEXT;
BEGIN
    -- Get post details
    SELECT p.*, 
           author.first_name as author_first_name,
           author.last_name as author_last_name
    INTO post_record
    FROM public.social_posts p
    JOIN public.profiles author ON p.user_id = author.id
    WHERE p.id = NEW.post_id;
    
    -- Get mentioned user details
    SELECT * INTO mentioned_user_record
    FROM public.profiles
    WHERE id = NEW.mentioned_user_id;
    
    -- Only create notification if user allows mentions and is not mentioning themselves
    IF mentioned_user_record.allow_mentions = true AND NEW.mentioned_user_id != NEW.mentioned_by_user_id THEN
        -- Clean the content by removing UUID format from mentions
        clean_content := REGEXP_REPLACE(post_record.content, '@\[([^\]]+)\]\(uuid:[a-f0-9-]+\)', '@\1', 'g');
        
        -- Create notification title and message
        notification_title := COALESCE(post_record.author_first_name, 'Someone') || ' mentioned you in a post';
        notification_message := 'You were mentioned in a social post: "' || 
                               LEFT(clean_content, 100) || 
                               CASE WHEN LENGTH(clean_content) > 100 THEN '...' ELSE '' END || '"';
        
        -- Create action URL to the post
        action_url := '/social/post/' || NEW.post_id::TEXT;
        
        -- Create notification data with clean content
        notification_data := jsonb_build_object(
            'post_id', NEW.post_id,
            'mentioned_by_user_id', NEW.mentioned_by_user_id,
            'mention_text', NEW.mention_text,
            'post_content_preview', LEFT(clean_content, 200)
        );
        
        -- Create notification
        PERFORM create_notification(
            NEW.mentioned_user_id,
            'social_mention'::notification_type_enum,
            notification_title,
            notification_message,
            NEW.post_id,
            'social_post',
            action_url,
            notification_data
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the comment mention notification function with proper UUID cleaning
CREATE OR REPLACE FUNCTION notify_mentioned_users_in_comment()
RETURNS TRIGGER AS $$
DECLARE
    mentioned_user_record RECORD;
    comment_record RECORD;
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
    clean_content TEXT;
    clean_post_content TEXT;
BEGIN
    -- Get comment and post details
    SELECT c.*, 
           author.first_name as author_first_name,
           author.last_name as author_last_name,
           p.content as post_content
    INTO comment_record
    FROM public.social_comments c
    JOIN public.profiles author ON c.user_id = author.id
    JOIN public.social_posts p ON c.post_id = p.id
    WHERE c.id = NEW.comment_id;
    
    -- Get mentioned user details
    SELECT * INTO mentioned_user_record
    FROM public.profiles
    WHERE id = NEW.mentioned_user_id;
    
    -- Only create notification if user allows mentions and is not mentioning themselves
    IF mentioned_user_record.allow_mentions = true AND NEW.mentioned_user_id != NEW.mentioned_by_user_id THEN
        -- Clean the content by removing UUID format from mentions
        clean_content := REGEXP_REPLACE(comment_record.content, '@\[([^\]]+)\]\(uuid:[a-f0-9-]+\)', '@\1', 'g');
        clean_post_content := REGEXP_REPLACE(comment_record.post_content, '@\[([^\]]+)\]\(uuid:[a-f0-9-]+\)', '@\1', 'g');
        
        -- Create notification title and message
        notification_title := COALESCE(comment_record.author_first_name, 'Someone') || ' mentioned you in a comment';
        notification_message := 'You were mentioned in a comment: "' || 
                               LEFT(clean_content, 100) || 
                               CASE WHEN LENGTH(clean_content) > 100 THEN '...' ELSE '' END || '"';
        
        -- Create action URL to the post (will scroll to comment)
        action_url := '/social/post/' || comment_record.post_id::TEXT || '#comment-' || NEW.comment_id::TEXT;
        
        -- Create notification data with clean content
        notification_data := jsonb_build_object(
            'comment_id', NEW.comment_id,
            'post_id', comment_record.post_id,
            'mentioned_by_user_id', NEW.mentioned_by_user_id,
            'mention_text', NEW.mention_text,
            'comment_content_preview', LEFT(clean_content, 200),
            'post_content_preview', LEFT(clean_post_content, 200)
        );
        
        -- Create notification
        PERFORM create_notification(
            NEW.mentioned_user_id,
            'social_mention'::notification_type_enum,
            notification_title,
            notification_message,
            comment_record.post_id,
            'social_comment',
            action_url,
            notification_data
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
