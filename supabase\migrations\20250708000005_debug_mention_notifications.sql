-- Add debugging to mention notification triggers
-- This migration adds logging to help debug notification issues

-- Update the post mention notification function with debugging
CREATE OR REPLACE FUNCTION notify_mentioned_users_in_post()
RETURNS TRIGGER AS $$
DECLARE
    mentioned_user_record RECORD;
    post_record RECORD;
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
BEGIN
    -- Log that trigger was called
    RAISE NOTICE 'Post mention trigger called for post_id: %, mentioned_user_id: %', NEW.post_id, NEW.mentioned_user_id;
    
    -- Get post details
    SELECT p.*, 
           author.first_name as author_first_name,
           author.last_name as author_last_name
    INTO post_record
    FROM public.social_posts p
    JOIN public.profiles author ON p.user_id = author.id
    WHERE p.id = NEW.post_id;
    
    RAISE NOTICE 'Post record found: %', post_record.id IS NOT NULL;
    
    -- Get mentioned user details
    SELECT * INTO mentioned_user_record
    FROM public.profiles
    WHERE id = NEW.mentioned_user_id;
    
    RAISE NOTICE 'Mentioned user found: %, allows mentions: %', 
        mentioned_user_record.id IS NOT NULL, 
        mentioned_user_record.allow_mentions;
    
    -- Only create notification if user allows mentions and is not mentioning themselves
    IF mentioned_user_record.allow_mentions = true AND NEW.mentioned_user_id != NEW.mentioned_by_user_id THEN
        RAISE NOTICE 'Creating notification for mention';
        
        -- Create notification title and message
        notification_title := COALESCE(post_record.author_first_name, 'Someone') || ' mentioned you in a post';
        notification_message := 'You were mentioned in a social post: "' || 
                               LEFT(post_record.content, 100) || 
                               CASE WHEN LENGTH(post_record.content) > 100 THEN '...' ELSE '' END || '"';
        
        -- Create action URL to the post
        action_url := '/social/post/' || NEW.post_id::TEXT;
        
        -- Create notification data
        notification_data := jsonb_build_object(
            'post_id', NEW.post_id,
            'mentioned_by_user_id', NEW.mentioned_by_user_id,
            'mention_text', NEW.mention_text,
            'post_content_preview', LEFT(post_record.content, 200)
        );
        
        -- Create notification
        PERFORM create_notification(
            NEW.mentioned_user_id,
            'social_mention'::notification_type_enum,
            notification_title,
            notification_message,
            NEW.post_id,
            'social_post',
            action_url,
            notification_data
        );
        
        RAISE NOTICE 'Notification created successfully';
    ELSE
        RAISE NOTICE 'Notification not created - user does not allow mentions or self-mention';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
