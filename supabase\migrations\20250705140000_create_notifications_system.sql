-- Create notifications system for the platform
-- This migration creates the notifications table and triggers for event-related notifications

-- Create notification type enum
CREATE TYPE notification_type_enum AS ENUM (
  'event_registration',
  'event_interest',
  'event_update',
  'event_reminder',
  'social_mention',
  'general'
);

-- Create notification status enum
CREATE TYPE notification_status_enum AS ENUM (
  'unread',
  'read',
  'archived'
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    
    -- Notification content
    type notification_type_enum NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    
    -- Associated data
    related_id UUID, -- Can be event_id, user_id, etc. depending on type
    related_type VARCHAR(50), -- 'event', 'user', etc.
    action_url TEXT, -- Optional URL to navigate to when notification is clicked
    
    -- Metadata
    status notification_status_enum DEFAULT 'unread',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional data as JSON for extensibility
    data JSONB DEFAULT '{}'::jsonb
);

-- Add indexes for performance
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_status ON public.notifications(status);
CREATE INDEX idx_notifications_type ON public.notifications(type);
CREATE INDEX idx_notifications_created_at ON public.notifications(created_at DESC);
CREATE INDEX idx_notifications_related ON public.notifications(related_id, related_type);

-- Create composite index for user notifications query
CREATE INDEX idx_notifications_user_status_created ON public.notifications(user_id, status, created_at DESC);

-- Enable Row Level Security (RLS)
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for notifications table
-- Users can only see their own notifications
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

-- Allow system to insert notifications (will be handled by functions)
CREATE POLICY "System can insert notifications" ON public.notifications
    FOR INSERT WITH CHECK (true);

-- Users can update their own notifications (mark as read/archived)
CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own notifications
CREATE POLICY "Users can delete their own notifications" ON public.notifications
    FOR DELETE USING (auth.uid() = user_id);

-- Function to create a notification
CREATE OR REPLACE FUNCTION create_notification(
    p_user_id UUID,
    p_type notification_type_enum,
    p_title VARCHAR(255),
    p_message TEXT,
    p_related_id UUID DEFAULT NULL,
    p_related_type VARCHAR(50) DEFAULT NULL,
    p_action_url TEXT DEFAULT NULL,
    p_data JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (
        user_id,
        type,
        title,
        message,
        related_id,
        related_type,
        action_url,
        data
    ) VALUES (
        p_user_id,
        p_type,
        p_title,
        p_message,
        p_related_id,
        p_related_type,
        p_action_url,
        p_data
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle event registration notifications
CREATE OR REPLACE FUNCTION notify_event_organizer_on_registration()
RETURNS TRIGGER AS $$
DECLARE
    event_record RECORD;
    organizer_id UUID;
    user_record RECORD;
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
BEGIN
    -- Only process if user is attending (registering for the event)
    IF NEW.is_attending = true AND (OLD.is_attending IS NULL OR OLD.is_attending = false) THEN
        -- Get event details and organizer
        SELECT e.*, e.created_by_user_id
        INTO event_record
        FROM events e
        WHERE e.id = NEW.event_id;
        
        -- Get user details
        SELECT p.first_name, p.last_name, p.email
        INTO user_record
        FROM profiles p
        WHERE p.id = NEW.user_id;
        
        -- Don't notify if user is registering for their own event
        IF event_record.created_by_user_id != NEW.user_id THEN
            -- Construct notification
            notification_title := 'New Event Registration';
            notification_message := COALESCE(user_record.first_name || ' ' || user_record.last_name, user_record.email, 'A user') 
                || ' has registered for your event "' || event_record.name || '"';
            
            action_url := '/events/' || NEW.event_id;
            
            notification_data := jsonb_build_object(
                'event_id', NEW.event_id,
                'event_name', event_record.name,
                'user_id', NEW.user_id,
                'user_name', COALESCE(user_record.first_name || ' ' || user_record.last_name, user_record.email),
                'registration_type', 'attending'
            );
            
            -- Create notification for event organizer
            PERFORM create_notification(
                event_record.created_by_user_id,
                'event_registration'::notification_type_enum,
                notification_title,
                notification_message,
                NEW.event_id,
                'event',
                action_url,
                notification_data
            );
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle event interest notifications
CREATE OR REPLACE FUNCTION notify_event_organizer_on_interest()
RETURNS TRIGGER AS $$
DECLARE
    event_record RECORD;
    organizer_id UUID;
    user_record RECORD;
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
BEGIN
    -- Only process if user is expressing interest (not registering)
    IF NEW.is_interested = true AND NEW.is_attending = false AND 
       (OLD.is_interested IS NULL OR OLD.is_interested = false) THEN
        
        -- Get event details and organizer
        SELECT e.*, e.created_by_user_id
        INTO event_record
        FROM events e
        WHERE e.id = NEW.event_id;
        
        -- Get user details
        SELECT p.first_name, p.last_name, p.email
        INTO user_record
        FROM profiles p
        WHERE p.id = NEW.user_id;
        
        -- Don't notify if user is expressing interest in their own event
        IF event_record.created_by_user_id != NEW.user_id THEN
            -- Construct notification
            notification_title := 'New Event Interest';
            notification_message := COALESCE(user_record.first_name || ' ' || user_record.last_name, user_record.email, 'A user') 
                || ' has expressed interest in your event "' || event_record.name || '"';
            
            action_url := '/events/' || NEW.event_id;
            
            notification_data := jsonb_build_object(
                'event_id', NEW.event_id,
                'event_name', event_record.name,
                'user_id', NEW.user_id,
                'user_name', COALESCE(user_record.first_name || ' ' || user_record.last_name, user_record.email),
                'registration_type', 'interested'
            );
            
            -- Create notification for event organizer
            PERFORM create_notification(
                event_record.created_by_user_id,
                'event_interest'::notification_type_enum,
                notification_title,
                notification_message,
                NEW.event_id,
                'event',
                action_url,
                notification_data
            );
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for event notifications
CREATE TRIGGER trigger_notify_event_registration
    AFTER INSERT OR UPDATE ON public.event_interests
    FOR EACH ROW
    EXECUTE FUNCTION notify_event_organizer_on_registration();

CREATE TRIGGER trigger_notify_event_interest
    AFTER INSERT OR UPDATE ON public.event_interests
    FOR EACH ROW
    EXECUTE FUNCTION notify_event_organizer_on_interest();

-- Add comments for documentation
COMMENT ON TABLE public.notifications IS 'System notifications for users including event registrations, interests, and updates';
COMMENT ON COLUMN public.notifications.type IS 'Type of notification to determine handling and display';
COMMENT ON COLUMN public.notifications.related_id IS 'ID of the related entity (event, user, etc.)';
COMMENT ON COLUMN public.notifications.related_type IS 'Type of the related entity for proper linking';
COMMENT ON COLUMN public.notifications.action_url IS 'URL to navigate to when notification is clicked';
COMMENT ON COLUMN public.notifications.data IS 'Additional structured data for the notification';

-- Function to mark notification as read
CREATE OR REPLACE FUNCTION mark_notification_read(notification_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE public.notifications 
    SET status = 'read', read_at = NOW()
    WHERE id = notification_id AND user_id = auth.uid();
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark all notifications as read for a user
CREATE OR REPLACE FUNCTION mark_all_notifications_read()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE public.notifications 
    SET status = 'read', read_at = NOW()
    WHERE user_id = auth.uid() AND status = 'unread';
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get unread notification count
CREATE OR REPLACE FUNCTION get_unread_notification_count()
RETURNS INTEGER AS $$
DECLARE
    count_result INTEGER;
BEGIN
    SELECT COUNT(*) INTO count_result
    FROM public.notifications
    WHERE user_id = auth.uid() AND status = 'unread';
    
    RETURN count_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
