import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { UserSearchService, type MentionableUser } from '../services/userSearchService';
import { cn } from '@/lib/utils';

interface MentionInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  maxLength?: number;
  rows?: number;
  onMentionSelect?: (user: MentionableUser, mentionText: string) => void;
}

interface MentionSuggestion {
  user: MentionableUser;
  startPos: number;
  endPos: number;
  query: string;
}

const MentionInput: React.FC<MentionInputProps> = ({
  value,
  onChange,
  placeholder = "What's on your mind?",
  className,
  disabled = false,
  maxLength,
  rows = 3,
  onMentionSelect
}) => {
  const [suggestions, setSuggestions] = useState<MentionableUser[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(0);
  const [currentMention, setCurrentMention] = useState<MentionSuggestion | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Find mention at cursor position
  const findMentionAtCursor = useCallback((text: string, cursorPos: number): MentionSuggestion | null => {
    // Look backwards from cursor to find @
    let startPos = cursorPos;
    while (startPos > 0 && text[startPos - 1] !== '@' && text[startPos - 1] !== ' ' && text[startPos - 1] !== '\n') {
      startPos--;
    }
    
    if (startPos === 0 || text[startPos - 1] !== '@') {
      return null;
    }
    
    startPos--; // Include the @
    
    // Look forwards to find end of mention
    let endPos = cursorPos;
    while (endPos < text.length && text[endPos] !== ' ' && text[endPos] !== '\n' && text[endPos] !== '@') {
      endPos++;
    }
    
    const mentionText = text.substring(startPos + 1, endPos); // Exclude @
    
    return {
      user: {} as MentionableUser, // Placeholder
      startPos,
      endPos,
      query: mentionText
    };
  }, []);

  // Search for users
  const searchUsers = useCallback(async (query: string) => {
    if (query.length < 1) {
      setSuggestions([]);
      return;
    }

    setIsLoading(true);
    try {
      const users = await UserSearchService.searchMentionableUsers(query, 8);
      setSuggestions(users);
    } catch (error) {
      console.error('Error searching users:', error);
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Handle text change
  const handleTextChange = useCallback((newValue: string) => {
    onChange(newValue);
    
    const textarea = textareaRef.current;
    if (!textarea) return;
    
    const cursorPos = textarea.selectionStart;
    const mention = findMentionAtCursor(newValue, cursorPos);
    
    if (mention && mention.query.length > 0) {
      setCurrentMention(mention);
      setShowSuggestions(true);
      setSelectedSuggestionIndex(0);
      searchUsers(mention.query);
    } else {
      setCurrentMention(null);
      setShowSuggestions(false);
      setSuggestions([]);
    }
  }, [onChange, findMentionAtCursor, searchUsers]);

  // Handle suggestion selection
  const selectSuggestion = useCallback((user: MentionableUser) => {
    if (!currentMention) return;
    
    const beforeMention = value.substring(0, currentMention.startPos);
    const afterMention = value.substring(currentMention.endPos);
    const mentionText = `@${user.display_name}`;
    const newValue = beforeMention + mentionText + afterMention;
    
    onChange(newValue);
    setShowSuggestions(false);
    setCurrentMention(null);
    setSuggestions([]);
    
    // Call callback if provided
    onMentionSelect?.(user, mentionText);
    
    // Focus back to textarea and position cursor after mention
    setTimeout(() => {
      const textarea = textareaRef.current;
      if (textarea) {
        textarea.focus();
        const newCursorPos = currentMention.startPos + mentionText.length;
        textarea.setSelectionRange(newCursorPos, newCursorPos);
      }
    }, 0);
  }, [currentMention, value, onChange, onMentionSelect]);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!showSuggestions || suggestions.length === 0) return;
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
      case 'Tab':
        if (suggestions[selectedSuggestionIndex]) {
          e.preventDefault();
          selectSuggestion(suggestions[selectedSuggestionIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setCurrentMention(null);
        setSuggestions([]);
        break;
    }
  }, [showSuggestions, suggestions, selectedSuggestionIndex, selectSuggestion]);

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current && 
        !suggestionsRef.current.contains(event.target as Node) &&
        textareaRef.current &&
        !textareaRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="relative">
      <Textarea
        ref={textareaRef}
        value={value}
        onChange={(e) => handleTextChange(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className={className}
        disabled={disabled}
        maxLength={maxLength}
        rows={rows}
      />
      
      {showSuggestions && suggestions.length > 0 && (
        <Card 
          ref={suggestionsRef}
          className="absolute z-50 mt-1 w-full max-w-sm shadow-lg"
        >
          <CardContent className="p-2">
            <div className="text-xs text-muted-foreground mb-2 px-2">
              {isLoading ? 'Searching...' : `${suggestions.length} user${suggestions.length !== 1 ? 's' : ''} found`}
            </div>
            <div className="max-h-48 overflow-y-auto">
              {suggestions.map((user, index) => (
                <div
                  key={user.id}
                  className={cn(
                    "flex items-center gap-3 p-2 rounded cursor-pointer transition-colors",
                    index === selectedSuggestionIndex 
                      ? "bg-accent text-accent-foreground" 
                      : "hover:bg-muted"
                  )}
                  onClick={() => selectSuggestion(user)}
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user.avatar_url || undefined} />
                    <AvatarFallback className="text-xs">
                      {user.first_name?.[0]}{user.last_name?.[0]}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate">
                      {user.display_name}
                    </div>
                    {user.job_title && (
                      <div className="text-xs text-muted-foreground truncate">
                        {user.job_title}
                      </div>
                    )}
                    {user.organisation_name && (
                      <div className="text-xs text-muted-foreground truncate">
                        {user.organisation_name}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default MentionInput;
