import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { useToast } from '@/hooks/use-toast'
import { EventService } from '../services/eventService'
import { NetZeroCategorySelector } from '@/components/netzero/NetZeroCategorySelector'
import { IndustrySelector } from '@/components/industries/IndustrySelector'
import type { EventFormData, EventLocationType, EventWithInterests } from '../types'
import { EVENT_LOCATION_TYPES, DEFAULT_TIMEZONE } from '../types'

interface AddEventFormProps {
  onSuccess?: () => void
  onCancel?: () => void
  event?: EventWithInterests
  isEditing?: boolean
}

const AddEventForm = ({ onSuccess, onCancel, event, isEditing = false }: AddEventFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedNetZeroCategories, setSelectedNetZeroCategories] = useState<string[]>([])
  const [selectedTargetIndustry, setSelectedTargetIndustry] = useState<string | null>(null)
  const { toast } = useToast()
  
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    reset
  } = useForm<EventFormData>({
    defaultValues: isEditing && event ? {
      name: event.name,
      description: event.description || '',
      organizer_name: event.organizer_name,
      organizer_email: event.organizer_email || '',
      organizer_phone: event.organizer_phone || '',
      start_date: event.start_date,
      start_time: event.start_time || '',
      end_date: event.end_date || '',
      end_time: event.end_time || '',
      timezone: event.timezone || DEFAULT_TIMEZONE,
      location_type: event.location_type,
      venue_name: event.venue_name || '',
      address_line_1: event.address_line_1 || '',
      address_line_2: event.address_line_2 || '',
      city: event.city || '',
      postcode: event.postcode || '',
      online_meeting_url: event.online_meeting_url || '',
      online_meeting_details: event.online_meeting_details || '',
      max_attendees: event.max_attendees || undefined,
      registration_required: event.registration_required || false,
      registration_url: event.registration_url || '',
      event_url: event.event_url || '',
      tags: event.tags || []
    } : {
      location_type: 'face_to_face',
      timezone: DEFAULT_TIMEZONE,
      start_date: new Date().toISOString().split('T')[0],
      registration_required: false
    }
  })

  const locationType = watch('location_type')
  const registrationRequired = watch('registration_required')

  const onSubmit = async (data: EventFormData) => {
    setIsSubmitting(true)
    
    try {
      // Prepare data for submission
      const submissionData = {
        ...data,
        start_date: data.start_date,
        end_date: data.end_date || null,
        start_time: data.start_time || null,
        end_time: data.end_time || null,
        max_attendees: data.max_attendees || null,
        tags: data.tags || [],
        target_industry_id: selectedTargetIndustry,
        net_zero_categories: selectedNetZeroCategories
      }

      if (isEditing && event) {
        await EventService.updateEvent(event.id, submissionData)
        toast({
          title: "Success", 
          description: "Event has been updated successfully!"
        })
      } else {
        await EventService.createEvent(submissionData)
        toast({
          title: "Success",
          description: "Event has been created successfully!"
        })
      }
      
      reset()
      onSuccess?.()
    } catch (error) {
      console.error('Error saving event:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : `Failed to ${isEditing ? 'update' : 'create'} event`,
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>{isEditing ? 'Edit Event' : 'Add New Event'}</CardTitle>
        <CardDescription>
          {isEditing ? 'Update event information' : 'Create a new event for the community'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            
            <div>
              <Label htmlFor="name">Event Name *</Label>
              <Input
                id="name"
                {...register('name', { required: 'Event name is required' })}
                placeholder="e.g., Net Zero Innovation Workshop"
              />
              {errors.name && (
                <p className="text-sm text-destructive mt-1">{errors.name.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Describe your event, what attendees can expect, and any important details..."
                rows={4}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="organizer_name">Organizer Name *</Label>
                <Input
                  id="organizer_name"
                  {...register('organizer_name', { required: 'Organizer name is required' })}
                  placeholder="Your name or organisation"
                />
                {errors.organizer_name && (
                  <p className="text-sm text-destructive mt-1">{errors.organizer_name.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="organizer_email">Organizer Email</Label>
                <Input
                  id="organizer_email"
                  type="email"
                  {...register('organizer_email')}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="organizer_phone">Organizer Phone</Label>
              <Input
                id="organizer_phone"
                {...register('organizer_phone')}
                placeholder="+44 ************"
              />
            </div>
          </div>

          {/* Date and Time */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Date & Time</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="start_date">Start Date *</Label>
                <Input
                  id="start_date"
                  type="date"
                  {...register('start_date', { required: 'Start date is required' })}
                />
                {errors.start_date && (
                  <p className="text-sm text-destructive mt-1">{errors.start_date.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="start_time">Start Time</Label>
                <Input
                  id="start_time"
                  type="time"
                  {...register('start_time')}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="end_date">End Date</Label>
                <Input
                  id="end_date"
                  type="date"
                  {...register('end_date')}
                />
              </div>

              <div>
                <Label htmlFor="end_time">End Time</Label>
                <Input
                  id="end_time"
                  type="time"
                  {...register('end_time')}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="timezone">Timezone</Label>
              <Input
                id="timezone"
                {...register('timezone')}
                placeholder="Europe/London"
              />
            </div>
          </div>

          {/* Location */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Location</h3>
            
            <div>
              <Label htmlFor="location_type">Location Type *</Label>
              <Select
                value={locationType}
                onValueChange={(value: EventLocationType) => setValue('location_type', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select location type" />
                </SelectTrigger>
                <SelectContent>
                  {EVENT_LOCATION_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.icon} {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {(locationType === 'face_to_face' || locationType === 'hybrid') && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="venue_name">Venue Name</Label>
                  <Input
                    id="venue_name"
                    {...register('venue_name')}
                    placeholder="Conference Center, Office Building, etc."
                  />
                </div>

                <div>
                  <Label htmlFor="address_line_1">Address Line 1</Label>
                  <Input
                    id="address_line_1"
                    {...register('address_line_1')}
                    placeholder="Street address"
                  />
                </div>

                <div>
                  <Label htmlFor="address_line_2">Address Line 2</Label>
                  <Input
                    id="address_line_2"
                    {...register('address_line_2')}
                    placeholder="Apartment, suite, etc. (optional)"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      {...register('city')}
                      placeholder="London"
                    />
                  </div>

                  <div>
                    <Label htmlFor="postcode">Postcode</Label>
                    <Input
                      id="postcode"
                      {...register('postcode')}
                      placeholder="SW1A 1AA"
                    />
                  </div>
                </div>
              </div>
            )}

            {(locationType === 'online' || locationType === 'hybrid') && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="online_meeting_url">Meeting URL</Label>
                  <Input
                    id="online_meeting_url"
                    type="url"
                    {...register('online_meeting_url')}
                    placeholder="https://zoom.us/j/123456789"
                  />
                </div>

                <div>
                  <Label htmlFor="online_meeting_details">Meeting Details</Label>
                  <Textarea
                    id="online_meeting_details"
                    {...register('online_meeting_details')}
                    placeholder="Meeting ID, password, or other joining instructions..."
                    rows={3}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Additional Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Additional Details</h3>
            
            <div>
              <Label htmlFor="max_attendees">Maximum Attendees</Label>
              <Input
                id="max_attendees"
                type="number"
                min="1"
                {...register('max_attendees', { valueAsNumber: true })}
                placeholder="Leave empty for unlimited"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="registration_required"
                checked={registrationRequired}
                onCheckedChange={(checked) => setValue('registration_required', checked as boolean)}
              />
              <Label htmlFor="registration_required">Registration Required</Label>
            </div>

            {registrationRequired && (
              <div>
                <Label htmlFor="registration_url">Registration URL</Label>
                <Input
                  id="registration_url"
                  type="url"
                  {...register('registration_url')}
                  placeholder="https://eventbrite.com/e/..."
                />
              </div>
            )}

            <div>
              <Label htmlFor="event_url">Event Website</Label>
              <Input
                id="event_url"
                type="url"
                {...register('event_url')}
                placeholder="https://example.com/event-details"
              />
            </div>

            {/* Net Zero Categories */}
            <div>
              <Label>Net Zero Categories (Optional)</Label>
              <p className="text-sm text-muted-foreground mb-2">
                Select categories that best describe this event to help users find relevant content.
              </p>
              <NetZeroCategorySelector
                selectedSubcategories={selectedNetZeroCategories}
                onSelectionChange={setSelectedNetZeroCategories}
                maxSelections={5}
                title=""
                description=""
              />
            </div>

            {/* Target Industry */}
            <div>
              <Label>Target Industry (Optional)</Label>
              <p className="text-sm text-muted-foreground mb-2">
                Select the primary industry this event is targeted towards.
              </p>
              <IndustrySelector
                selectedIndustryId={selectedTargetIndustry}
                onIndustryChange={setSelectedTargetIndustry}
                mode="single"
                title=""
                description=""
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex gap-4 pt-6">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1"
            >
              {isSubmitting ? 'Creating Event...' : 'Create Event'}
            </Button>
            
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

export default AddEventForm
