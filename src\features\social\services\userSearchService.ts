import { supabase } from '@/integrations/supabase/client';

// User search result interface for mentions
export interface MentionableUser {
  id: string;
  first_name: string | null;
  last_name: string | null;
  job_title: string | null;
  organisation_name: string | null;
  avatar_url: string | null;
  display_name: string;
}

export class UserSearchService {
  /**
   * Search for users that can be mentioned
   * Uses the database function that filters by mention preferences
   */
  static async searchMentionableUsers(query: string, limit: number = 10): Promise<MentionableUser[]> {
    if (!query || query.trim().length < 1) {
      return [];
    }

    try {
      const { data, error } = await supabase
        .rpc('search_mentionable_users', {
          search_query: query.trim(),
          limit_count: limit
        });

      if (error) {
        console.error('Error searching mentionable users:', error);
        throw new Error(`Failed to search users: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('Error in searchMentionableUsers:', error);
      throw error;
    }
  }

  /**
   * Get user by ID for mention validation
   */
  static async getUserById(userId: string): Promise<MentionableUser | null> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          first_name,
          last_name,
          job_title,
          organisation_name,
          avatar_url
        `)
        .eq('id', userId)
        .eq('profile_visible', true)
        .eq('allow_mentions', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        throw new Error(`Failed to get user: ${error.message}`);
      }

      if (!data) {
        return null;
      }

      // Create display name
      const display_name = data.first_name && data.last_name 
        ? `${data.first_name} ${data.last_name}`
        : data.first_name || data.last_name || 'Anonymous User';

      return {
        ...data,
        display_name
      };
    } catch (error) {
      console.error('Error in getUserById:', error);
      throw error;
    }
  }

  /**
   * Get multiple users by IDs for mention validation
   */
  static async getUsersByIds(userIds: string[]): Promise<MentionableUser[]> {
    if (!userIds || userIds.length === 0) {
      return [];
    }

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          first_name,
          last_name,
          job_title,
          organisation_name,
          avatar_url
        `)
        .in('id', userIds)
        .eq('profile_visible', true)
        .eq('allow_mentions', true);

      if (error) {
        throw new Error(`Failed to get users: ${error.message}`);
      }

      return (data || []).map(user => ({
        ...user,
        display_name: user.first_name && user.last_name 
          ? `${user.first_name} ${user.last_name}`
          : user.first_name || user.last_name || 'Anonymous User'
      }));
    } catch (error) {
      console.error('Error in getUsersByIds:', error);
      throw error;
    }
  }

  /**
   * Check if a user allows mentions
   */
  static async canUserBeMentioned(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('allow_mentions, profile_visible')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error checking mention permissions:', error);
        return false;
      }

      return data?.allow_mentions === true && data?.profile_visible === true;
    } catch (error) {
      console.error('Error in canUserBeMentioned:', error);
      return false;
    }
  }

  /**
   * Format user display name for mentions
   */
  static formatDisplayName(user: MentionableUser): string {
    if (user.first_name && user.last_name) {
      return `${user.first_name} ${user.last_name}`;
    }
    return user.first_name || user.last_name || 'Anonymous User';
  }

  /**
   * Create mention text for a user (what appears in the content)
   */
  static createMentionText(user: MentionableUser): string {
    const displayName = this.formatDisplayName(user);
    return `@${displayName}`;
  }
}
