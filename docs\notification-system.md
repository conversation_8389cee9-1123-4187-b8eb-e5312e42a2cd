# Nexzero Notification System Documentation

## 📋 **Current Notification Types**

### **1. Event Notifications**

#### **Event Registration (`event_registration`)**
- **Trigger**: When a user registers for an event (sets interest to "attending")
- **Recipient**: Event creator/organizer
- **Title**: `"{User Name} registered for your event"`
- **Message**: `"{User Name} has registered to attend your event: {Event Title}"`
- **Action URL**: `/events/{event_id}`
- **Data**: 
  ```json
  {
    "event_id": "uuid",
    "user_id": "uuid", 
    "user_name": "string",
    "registration_type": "attending"
  }
  ```

#### **Event Interest (`event_interest`)**
- **Trigger**: When a user shows interest in an event (sets interest to "interested")
- **Recipient**: Event creator/organizer
- **Title**: `"{User Name} is interested in your event"`
- **Message**: `"{User Name} has shown interest in your event: {Event Title}"`
- **Action URL**: `/events/{event_id}`
- **Data**:
  ```json
  {
    "event_id": "uuid",
    "user_id": "uuid",
    "user_name": "string", 
    "registration_type": "interested"
  }
  ```

#### **Event Update (`event_update`)**
- **Status**: Enum defined but not implemented
- **Intended Use**: When event details are modified

#### **Event Reminder (`event_reminder`)**
- **Status**: Enum defined but not implemented
- **Intended Use**: Automated reminders before events

### **2. Social Notifications**

#### **Social Mention (`social_mention`)**
- **Trigger**: When a user is mentioned in a social post or comment
- **Recipient**: Mentioned user (excluding self-mentions)
- **Title**: `"{Author Name} mentioned you in a {post/comment}"`
- **Message**: `"You were mentioned in a social {post/comment}: {content preview}"`
- **Action URL**: 
  - Posts: `/social/post/{post_id}`
  - Comments: `/social/post/{post_id}#comment-{comment_id}`
- **Data**:
  ```json
  {
    "post_id": "uuid",
    "comment_id": "uuid", // Only for comment mentions
    "mentioned_by_user_id": "uuid",
    "mention_text": "string",
    "post_content_preview": "string",
    "comment_content_preview": "string" // Only for comment mentions
  }
  ```

### **3. General Notifications**

#### **General (`general`)**
- **Status**: Enum defined but not implemented
- **Intended Use**: System announcements, admin messages

---

## 🎯 **Proposed New Notification Types**

### **1. Content Interest Matching**

#### **New Event Match (`event_interest_match`)**
- **Trigger**: When a new event is created that matches user's net zero categories or industries
- **Recipient**: Users with matching interests
- **Title**: `"New event in {category/industry}"`
- **Message**: `"A new event '{Event Title}' has been added in {category/industry} that might interest you"`
- **Action URL**: `/events/{event_id}`

#### **New Funding Match (`funding_interest_match`)**
- **Trigger**: When new funding opportunity matches user's interests
- **Recipient**: Users with matching interests
- **Title**: `"New funding opportunity in {category/industry}"`
- **Message**: `"A new funding opportunity '{Funding Title}' has been added that might interest you"`
- **Action URL**: `/funding/{funding_id}`

#### **New Business Match (`business_interest_match`)**
- **Trigger**: When new business is added that matches user's interests
- **Recipient**: Users with matching interests
- **Title**: `"New business in {category/industry}"`
- **Message**: `"A new business '{Business Name}' has been added in {category/industry}"`
- **Action URL**: `/businesses/{business_id}`

### **2. Social Engagement**

#### **Post Reaction (`post_reaction`)**
- **Trigger**: When someone reacts to user's post
- **Recipient**: Post author
- **Title**: `"{User Name} reacted to your post"`
- **Message**: `"{User Name} {reaction_type} your post"`

#### **Comment on Post (`post_comment`)**
- **Trigger**: When someone comments on user's post
- **Recipient**: Post author
- **Title**: `"{User Name} commented on your post"`

#### **Follow User (`user_follow`)**
- **Trigger**: When someone follows the user
- **Recipient**: Followed user
- **Title**: `"{User Name} started following you"`

### **3. Business & Content**

#### **Business Product Added (`business_product_added`)**
- **Trigger**: When a product is added to user's business
- **Recipient**: Business followers/interested users

#### **Funding Application (`funding_application`)**
- **Trigger**: When someone applies for user's funding opportunity
- **Recipient**: Funding creator

---

## 🏗️ **Database Schema**

### **Notifications Table**
```sql
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES profiles(id),
    type notification_type_enum NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    related_id UUID,
    related_type VARCHAR(50),
    action_url TEXT,
    data JSONB DEFAULT '{}'::jsonb,
    read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **Notification Types Enum**
```sql
CREATE TYPE notification_type_enum AS ENUM (
    'event_registration',
    'event_interest', 
    'event_update',
    'event_reminder',
    'social_mention',
    'general'
);
```

---

## 🔧 **Implementation Architecture**

### **Current Components**
1. **Database Functions**: `create_notification()`, trigger functions
2. **Service Layer**: `NotificationService` in TypeScript
3. **UI Components**: Notification bell, notification list
4. **Real-time**: Supabase subscriptions for live updates

### **Key Functions**
- `create_notification()` - Creates new notifications
- `notify_event_organizer_on_registration()` - Event registration trigger
- `notify_event_organizer_on_interest()` - Event interest trigger  
- `notify_mentioned_users_in_post()` - Social mention trigger
- `notify_mentioned_users_in_comment()` - Comment mention trigger

---

## 📊 **User Preferences**

### **Current Settings**
- `allow_mentions` - Whether user can be mentioned in social posts

### **Proposed Settings**
- `notify_interest_matches` - Get notified about new content matching interests
- `notify_social_reactions` - Get notified about post reactions
- `notify_social_comments` - Get notified about comments on posts
- `notify_follows` - Get notified about new followers
- `notify_business_activity` - Get notified about business-related activity

---

## 🎯 **Next Steps for Implementation**

1. **Add new notification types to enum**
2. **Create interest matching triggers**
3. **Implement user preference settings**
4. **Add social engagement notifications**
5. **Create notification batching/digest system**
6. **Add email notification options**
7. **Implement notification categories/filtering**

---

## 🔍 **Technical Notes**

- All notifications include clean content (UUIDs removed from mentions)
- Self-notifications are prevented where appropriate
- RLS policies protect notification access
- Real-time updates via Supabase subscriptions
- Notification data stored as JSONB for flexibility
