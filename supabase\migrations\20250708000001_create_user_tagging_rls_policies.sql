-- Create RLS policies for user tagging/mention system
-- This migration creates Row Level Security policies for mention tables

-- Enable RLS on mention tables
ALTER TABLE public.social_post_mentions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.social_comment_mentions ENABLE ROW LEVEL SECURITY;

-- Social Post Mentions Policies

-- Users can view mentions in posts they can see
CREATE POLICY "Users can view post mentions for visible posts" ON public.social_post_mentions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.social_posts p
            WHERE p.id = social_post_mentions.post_id
            AND p.is_deleted = false
        )
    );

-- Users can create mentions when creating posts (handled by application logic)
CREATE POLICY "Users can create post mentions for their own posts" ON public.social_post_mentions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.social_posts p
            WHERE p.id = social_post_mentions.post_id
            AND p.user_id = auth.uid()
            AND p.is_deleted = false
        )
        AND mentioned_by_user_id = auth.uid()
    );

-- Users can delete mentions from their own posts
CREATE POLICY "Users can delete mentions from their own posts" ON public.social_post_mentions
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.social_posts p
            WHERE p.id = social_post_mentions.post_id
            AND p.user_id = auth.uid()
        )
    );

-- Social Comment Mentions Policies

-- Users can view mentions in comments for posts they can see
CREATE POLICY "Users can view comment mentions for visible comments" ON public.social_comment_mentions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.social_comments c
            JOIN public.social_posts p ON c.post_id = p.id
            WHERE c.id = social_comment_mentions.comment_id
            AND c.is_deleted = false
            AND p.is_deleted = false
        )
    );

-- Users can create mentions when creating comments (handled by application logic)
CREATE POLICY "Users can create comment mentions for their own comments" ON public.social_comment_mentions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.social_comments c
            WHERE c.id = social_comment_mentions.comment_id
            AND c.user_id = auth.uid()
            AND c.is_deleted = false
        )
        AND mentioned_by_user_id = auth.uid()
    );

-- Users can delete mentions from their own comments
CREATE POLICY "Users can delete mentions from their own comments" ON public.social_comment_mentions
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.social_comments c
            WHERE c.id = social_comment_mentions.comment_id
            AND c.user_id = auth.uid()
        )
    );

-- Additional policies for mentioned users

-- Mentioned users can view their own mentions
CREATE POLICY "Users can view mentions of themselves in posts" ON public.social_post_mentions
    FOR SELECT USING (mentioned_user_id = auth.uid());

CREATE POLICY "Users can view mentions of themselves in comments" ON public.social_comment_mentions
    FOR SELECT USING (mentioned_user_id = auth.uid());

-- Add comments for documentation
COMMENT ON POLICY "Users can view post mentions for visible posts" ON public.social_post_mentions IS 'Allow viewing mentions in posts that are visible to the user';
COMMENT ON POLICY "Users can create post mentions for their own posts" ON public.social_post_mentions IS 'Allow users to create mentions only in their own posts';
COMMENT ON POLICY "Users can delete mentions from their own posts" ON public.social_post_mentions IS 'Allow users to delete mentions from their own posts';
COMMENT ON POLICY "Users can view comment mentions for visible comments" ON public.social_comment_mentions IS 'Allow viewing mentions in comments for visible posts';
COMMENT ON POLICY "Users can create comment mentions for their own comments" ON public.social_comment_mentions IS 'Allow users to create mentions only in their own comments';
COMMENT ON POLICY "Users can delete mentions from their own comments" ON public.social_comment_mentions IS 'Allow users to delete mentions from their own comments';
COMMENT ON POLICY "Users can view mentions of themselves in posts" ON public.social_post_mentions IS 'Allow users to see when they are mentioned in posts';
COMMENT ON POLICY "Users can view mentions of themselves in comments" ON public.social_comment_mentions IS 'Allow users to see when they are mentioned in comments';
