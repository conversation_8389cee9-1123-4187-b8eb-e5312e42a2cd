-- Create Social Mention System
-- This migration creates the complete user tagging/mention system for social posts and comments

-- Add allow_mentions setting to profiles
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS allow_mentions BOOLEAN DEFAULT true;

-- Update existing users to allow mentions by default
UPDATE public.profiles 
SET allow_mentions = true 
WHERE allow_mentions IS NULL;

-- Add social_mention to notification types
ALTER TYPE notification_type_enum ADD VALUE IF NOT EXISTS 'social_mention';

-- Create social post mentions table
CREATE TABLE IF NOT EXISTS public.social_post_mentions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES public.social_posts(id) ON DELETE CASCADE,
    mentioned_user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    mentioned_by_user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    mention_text TEXT NOT NULL, -- The actual mention text like "@<PERSON>"
    position_start INTEGER NOT NULL, -- Start position of mention in content
    position_end INTEGER NOT NULL, -- End position of mention in content
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(post_id, mentioned_user_id)
);

-- Create social comment mentions table
CREATE TABLE IF NOT EXISTS public.social_comment_mentions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    comment_id UUID NOT NULL REFERENCES public.social_comments(id) ON DELETE CASCADE,
    mentioned_user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    mentioned_by_user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    mention_text TEXT NOT NULL, -- The actual mention text like "@John Doe"
    position_start INTEGER NOT NULL, -- Start position of mention in content
    position_end INTEGER NOT NULL, -- End position of mention in content
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(comment_id, mentioned_user_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_social_post_mentions_post_id ON public.social_post_mentions(post_id);
CREATE INDEX IF NOT EXISTS idx_social_post_mentions_mentioned_user_id ON public.social_post_mentions(mentioned_user_id);
CREATE INDEX IF NOT EXISTS idx_social_comment_mentions_comment_id ON public.social_comment_mentions(comment_id);
CREATE INDEX IF NOT EXISTS idx_social_comment_mentions_mentioned_user_id ON public.social_comment_mentions(mentioned_user_id);

-- Function to search mentionable users
CREATE OR REPLACE FUNCTION search_mentionable_users(search_query TEXT, result_limit INTEGER DEFAULT 10)
RETURNS TABLE (
    id UUID,
    first_name TEXT,
    last_name TEXT,
    display_name TEXT,
    avatar_url TEXT,
    job_title TEXT,
    organisation_name TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.first_name,
        p.last_name,
        COALESCE(
            CASE 
                WHEN p.first_name IS NOT NULL AND p.last_name IS NOT NULL 
                THEN p.first_name || ' ' || p.last_name
                WHEN p.first_name IS NOT NULL 
                THEN p.first_name
                WHEN p.last_name IS NOT NULL 
                THEN p.last_name
                ELSE SPLIT_PART(auth.email, '@', 1)
            END,
            'User'
        ) as display_name,
        p.avatar_url,
        p.job_title,
        p.organisation_name
    FROM public.profiles p
    LEFT JOIN auth.users auth ON p.id = auth.id
    WHERE 
        p.profile_visible = true 
        AND p.allow_mentions = true
        AND (
            p.first_name ILIKE '%' || search_query || '%' OR
            p.last_name ILIKE '%' || search_query || '%' OR
            (p.first_name || ' ' || p.last_name) ILIKE '%' || search_query || '%' OR
            p.job_title ILIKE '%' || search_query || '%' OR
            p.organisation_name ILIKE '%' || search_query || '%'
        )
    ORDER BY 
        -- Prioritize exact matches
        CASE 
            WHEN (p.first_name || ' ' || p.last_name) ILIKE search_query THEN 1
            WHEN p.first_name ILIKE search_query OR p.last_name ILIKE search_query THEN 2
            ELSE 3
        END,
        p.first_name, p.last_name
    LIMIT result_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to notify mentioned users in posts
CREATE OR REPLACE FUNCTION notify_mentioned_users_in_post()
RETURNS TRIGGER AS $$
DECLARE
    mentioned_user_record RECORD;
    post_record RECORD;
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
    clean_content TEXT;
BEGIN
    -- Get post details
    SELECT p.*, 
           author.first_name as author_first_name,
           author.last_name as author_last_name
    INTO post_record
    FROM public.social_posts p
    JOIN public.profiles author ON p.user_id = author.id
    WHERE p.id = NEW.post_id;
    
    -- Get mentioned user details
    SELECT * INTO mentioned_user_record
    FROM public.profiles
    WHERE id = NEW.mentioned_user_id;
    
    -- Only create notification if user allows mentions and is not mentioning themselves
    IF mentioned_user_record.allow_mentions = true AND NEW.mentioned_user_id != NEW.mentioned_by_user_id THEN
        -- Clean the content by removing UUID format from mentions
        clean_content := REGEXP_REPLACE(post_record.content, '@\[([^\]]+)\]\(uuid:[a-f0-9-]+\)', '@\1', 'g');
        
        -- Create notification title and message
        notification_title := COALESCE(post_record.author_first_name, 'Someone') || ' mentioned you in a post';
        notification_message := 'You were mentioned in a social post: "' || 
                               LEFT(clean_content, 100) || 
                               CASE WHEN LENGTH(clean_content) > 100 THEN '...' ELSE '' END || '"';
        
        -- Create action URL to the post
        action_url := '/social/post/' || NEW.post_id::TEXT;
        
        -- Create notification data with clean content
        notification_data := jsonb_build_object(
            'post_id', NEW.post_id,
            'mentioned_by_user_id', NEW.mentioned_by_user_id,
            'mention_text', NEW.mention_text,
            'post_content_preview', LEFT(clean_content, 200)
        );
        
        -- Create notification
        PERFORM create_notification(
            NEW.mentioned_user_id,
            'social_mention'::notification_type_enum,
            notification_title,
            notification_message,
            NEW.post_id,
            'social_post',
            action_url,
            notification_data
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to notify mentioned users in comments
CREATE OR REPLACE FUNCTION notify_mentioned_users_in_comment()
RETURNS TRIGGER AS $$
DECLARE
    mentioned_user_record RECORD;
    comment_record RECORD;
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
    clean_content TEXT;
    clean_post_content TEXT;
BEGIN
    -- Get comment and post details
    SELECT c.*, 
           author.first_name as author_first_name,
           author.last_name as author_last_name,
           p.content as post_content
    INTO comment_record
    FROM public.social_comments c
    JOIN public.profiles author ON c.user_id = author.id
    JOIN public.social_posts p ON c.post_id = p.id
    WHERE c.id = NEW.comment_id;
    
    -- Get mentioned user details
    SELECT * INTO mentioned_user_record
    FROM public.profiles
    WHERE id = NEW.mentioned_user_id;
    
    -- Only create notification if user allows mentions and is not mentioning themselves
    IF mentioned_user_record.allow_mentions = true AND NEW.mentioned_user_id != NEW.mentioned_by_user_id THEN
        -- Clean the content by removing UUID format from mentions
        clean_content := REGEXP_REPLACE(comment_record.content, '@\[([^\]]+)\]\(uuid:[a-f0-9-]+\)', '@\1', 'g');
        clean_post_content := REGEXP_REPLACE(comment_record.post_content, '@\[([^\]]+)\]\(uuid:[a-f0-9-]+\)', '@\1', 'g');
        
        -- Create notification title and message
        notification_title := COALESCE(comment_record.author_first_name, 'Someone') || ' mentioned you in a comment';
        notification_message := 'You were mentioned in a comment: "' || 
                               LEFT(clean_content, 100) || 
                               CASE WHEN LENGTH(clean_content) > 100 THEN '...' ELSE '' END || '"';
        
        -- Create action URL to the post (will scroll to comment)
        action_url := '/social/post/' || comment_record.post_id::TEXT || '#comment-' || NEW.comment_id::TEXT;
        
        -- Create notification data with clean content
        notification_data := jsonb_build_object(
            'comment_id', NEW.comment_id,
            'post_id', comment_record.post_id,
            'mentioned_by_user_id', NEW.mentioned_by_user_id,
            'mention_text', NEW.mention_text,
            'comment_content_preview', LEFT(clean_content, 200),
            'post_content_preview', LEFT(clean_post_content, 200)
        );
        
        -- Create notification
        PERFORM create_notification(
            NEW.mentioned_user_id,
            'social_mention'::notification_type_enum,
            notification_title,
            notification_message,
            comment_record.post_id,
            'social_comment',
            action_url,
            notification_data
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for mention notifications
CREATE TRIGGER trigger_notify_post_mentions
    AFTER INSERT ON public.social_post_mentions
    FOR EACH ROW
    EXECUTE FUNCTION notify_mentioned_users_in_post();

CREATE TRIGGER trigger_notify_comment_mentions
    AFTER INSERT ON public.social_comment_mentions
    FOR EACH ROW
    EXECUTE FUNCTION notify_mentioned_users_in_comment();

-- RLS Policies
ALTER TABLE public.social_post_mentions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.social_comment_mentions ENABLE ROW LEVEL SECURITY;

-- Users can view mentions where they are mentioned or are the author of the post/comment
CREATE POLICY "Users can view post mentions" ON public.social_post_mentions
    FOR SELECT USING (
        auth.uid() = mentioned_user_id OR 
        auth.uid() = mentioned_by_user_id OR
        auth.uid() IN (SELECT user_id FROM public.social_posts WHERE id = post_id)
    );

CREATE POLICY "Users can view comment mentions" ON public.social_comment_mentions
    FOR SELECT USING (
        auth.uid() = mentioned_user_id OR 
        auth.uid() = mentioned_by_user_id OR
        auth.uid() IN (SELECT user_id FROM public.social_comments WHERE id = comment_id)
    );

-- Users can create mentions in their own posts/comments
CREATE POLICY "Users can create post mentions" ON public.social_post_mentions
    FOR INSERT WITH CHECK (
        auth.uid() = mentioned_by_user_id AND
        auth.uid() IN (SELECT user_id FROM public.social_posts WHERE id = post_id)
    );

CREATE POLICY "Users can create comment mentions" ON public.social_comment_mentions
    FOR INSERT WITH CHECK (
        auth.uid() = mentioned_by_user_id AND
        auth.uid() IN (SELECT user_id FROM public.social_comments WHERE id = comment_id)
    );

-- Grant permissions
GRANT ALL ON TABLE public.social_post_mentions TO authenticated;
GRANT ALL ON TABLE public.social_comment_mentions TO authenticated;
GRANT ALL ON TABLE public.social_post_mentions TO service_role;
GRANT ALL ON TABLE public.social_comment_mentions TO service_role;

GRANT EXECUTE ON FUNCTION search_mentionable_users(TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION search_mentionable_users(TEXT, INTEGER) TO service_role;

-- Add comments for documentation
COMMENT ON TABLE public.social_post_mentions IS 'User mentions in social posts with notification triggers';
COMMENT ON TABLE public.social_comment_mentions IS 'User mentions in social comments with notification triggers';
COMMENT ON COLUMN public.profiles.allow_mentions IS 'Whether user allows being mentioned in social posts and comments';
