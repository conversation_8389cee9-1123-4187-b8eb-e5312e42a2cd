// Temporary types until database types are regenerated
export interface FundingOpportunity {
  id: string
  created_by_user_id: string
  name: string
  organization_name: string
  funding_type: 'grant' | 'loan' | 'tender' | 'private_investment' | 'other'
  date_listed: string | null
  deadline_date: string | null
  description: string | null
  eligibility_criteria: string | null
  url: string | null
  amount_min: number | null
  amount_max: number | null
  currency: string | null
  target_industry_id: string | null
  created_at: string
  updated_at: string
}

export interface NewFundingOpportunity {
  created_by_user_id?: string
  name: string
  organization_name: string
  funding_type?: 'grant' | 'loan' | 'tender' | 'private_investment' | 'other'
  date_listed?: string | null
  deadline_date?: string | null
  description?: string | null
  eligibility_criteria?: string | null
  url?: string | null
  amount_min?: number | null
  amount_max?: number | null
  currency?: string | null
  target_industry_id?: string | null
}

export interface FundingOpportunityInterest {
  id: string
  funding_opportunity_id: string
  user_id: string
  is_interested: boolean
  wants_collaboration: boolean
  note: string | null
  created_at: string
  updated_at: string
}

export interface NewFundingOpportunityInterest {
  funding_opportunity_id: string
  user_id?: string
  is_interested?: boolean
  wants_collaboration?: boolean
  note?: string | null
}

// Enums
export type FundingType = 'grant' | 'loan' | 'tender' | 'private_investment' | 'other'

// Form data interfaces
export interface FundingOpportunityFormData {
  name: string
  organization_name: string
  funding_type: FundingType
  date_listed?: string
  deadline_date?: string
  description?: string
  eligibility_criteria?: string
  url?: string
  amount_min?: number
  amount_max?: number
  currency?: string
  net_zero_categories?: string[] // Array of subcategory IDs
  target_industry_id?: string
}

// Extended types with related data
export interface FundingOpportunityWithCreator extends FundingOpportunity {
  creator?: {
    id: string
    first_name?: string
    last_name?: string
    avatar_url?: string
  }
}

export interface FundingOpportunityWithInterests extends FundingOpportunityWithCreator {
  interests?: FundingOpportunityInterest[]
  interest_count?: number
  collaboration_count?: number
  user_interest?: FundingOpportunityInterest | null
}

// Interest form data
export interface FundingInterestFormData {
  is_interested: boolean
  wants_collaboration: boolean
  note?: string
}

// Filter and search interfaces
export interface FundingOpportunityFilters {
  funding_type?: FundingType[]
  organization_name?: string
  deadline_after?: string
  deadline_before?: string
  has_deadline?: boolean
  search_query?: string
}

export interface FundingOpportunitySortOptions {
  field: 'date_listed' | 'deadline_date' | 'name' | 'organization_name'
  direction: 'asc' | 'desc'
}

// API response types
export interface FundingOpportunityListResponse {
  data: FundingOpportunityWithInterests[]
  count: number
  page: number
  limit: number
  total_pages: number
}

// Constants
export const FUNDING_TYPES: { value: FundingType; label: string; description: string }[] = [
  {
    value: 'grant',
    label: 'Grant',
    description: 'Non-repayable funding typically from government or foundations'
  },
  {
    value: 'loan',
    label: 'Loan',
    description: 'Repayable funding with interest terms'
  },
  {
    value: 'tender',
    label: 'Tender',
    description: 'Competitive bidding process for contracts'
  },
  {
    value: 'private_investment',
    label: 'Private Investment',
    description: 'Investment from private investors or venture capital'
  },
  {
    value: 'other',
    label: 'Other',
    description: 'Other types of funding opportunities'
  }
]

export const CURRENCY_OPTIONS = [
  { value: 'GBP', label: '£ (GBP)', symbol: '£' },
  { value: 'USD', label: '$ (USD)', symbol: '$' },
  { value: 'EUR', label: '€ (EUR)', symbol: '€' }
]

export const DEFAULT_CURRENCY = 'GBP'
