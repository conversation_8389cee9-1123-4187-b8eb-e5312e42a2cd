import type { MentionableUser } from '../services/userSearchService';

// Interface for parsed mentions
export interface ParsedMention {
  userId: string;
  mentionText: string;
  startPosition: number;
  endPosition: number;
  displayName: string;
}

// Interface for mention data to be saved to database
export interface MentionData {
  mentioned_user_id: string;
  mention_text: string;
  position_start: number;
  position_end: number;
}

// Interface for mention replacement in content
export interface MentionReplacement {
  originalText: string;
  replacementText: string;
  startPosition: number;
  endPosition: number;
  userId: string;
}

export class MentionParser {
  // Regex to match @mentions with UUID format: @[Display Name](uuid:12345-67890)
  private static readonly MENTION_REGEX = /@\[([^\]]+)\]\(uuid:([a-f0-9-]+)\)/g;
  
  /**
   * Parse text content to find potential mentions
   * Returns array of mention matches with positions
   */
  static findMentionMatches(content: string): Array<{
    text: string;
    startPosition: number;
    endPosition: number;
    displayName?: string;
    userId?: string;
  }> {
    const matches: Array<{
      text: string;
      startPosition: number;
      endPosition: number;
      displayName?: string;
      userId?: string;
    }> = [];

    let match;
    // Create a fresh regex instance to avoid global state issues
    // Matches @[Display Name](uuid:12345-67890) format
    const regex = /@\[([^\]]+)\]\(uuid:([a-f0-9-]+)\)/g;

    while ((match = regex.exec(content)) !== null) {
      matches.push({
        text: match[0], // Full match including @[name](uuid:id)
        startPosition: match.index,
        endPosition: match.index + match[0].length,
        displayName: match[1], // The display name from [Display Name]
        userId: match[2] // The UUID from (uuid:12345-67890)
      });
    }

    return matches;
  }

  /**
   * Parse content and extract mentions with UUIDs
   * Returns validated mentions that contain user IDs
   */
  static parseMentions(content: string, availableUsers: MentionableUser[]): ParsedMention[] {
    const mentionMatches = this.findMentionMatches(content);
    const parsedMentions: ParsedMention[] = [];

    for (const match of mentionMatches) {
      // With the new format, we have the UUID directly from the match
      if (match.userId && match.displayName) {
        const parsedMention = {
          userId: match.userId,
          mentionText: match.text,
          startPosition: match.startPosition,
          endPosition: match.endPosition,
          displayName: match.displayName
        };
        parsedMentions.push(parsedMention);
      }
    }

    return parsedMentions;
  }

  /**
   * Find a user that matches the mention text
   */
  private static findMatchingUser(nameText: string, users: MentionableUser[]): MentionableUser | null {
    const normalizedName = nameText.toLowerCase().trim();
    
    // Try exact match first
    for (const user of users) {
      const userDisplayName = user.display_name.toLowerCase().trim();
      if (userDisplayName === normalizedName) {
        return user;
      }
    }

    // Try partial match
    for (const user of users) {
      const userDisplayName = user.display_name.toLowerCase().trim();
      if (userDisplayName.includes(normalizedName) || normalizedName.includes(userDisplayName)) {
        return user;
      }
    }

    return null;
  }

  /**
   * Convert parsed mentions to database format
   */
  static mentionsToDbFormat(mentions: ParsedMention[]): MentionData[] {
    return mentions.map(mention => ({
      mentioned_user_id: mention.userId,
      mention_text: mention.mentionText,
      position_start: mention.startPosition,
      position_end: mention.endPosition
    }));
  }

  /**
   * Replace mentions in content with clickable links for display
   * This is used when rendering content with mentions
   */
  static replaceMentionsWithLinks(
    content: string, 
    mentions: ParsedMention[]
  ): string {
    if (!mentions || mentions.length === 0) {
      return content;
    }

    // Sort mentions by position (descending) to replace from end to start
    const sortedMentions = [...mentions].sort((a, b) => b.startPosition - a.startPosition);
    
    let result = content;
    
    for (const mention of sortedMentions) {
      const beforeMention = result.substring(0, mention.startPosition);
      const afterMention = result.substring(mention.endPosition);
      
      // Create a clickable link (this will be processed by the display component)
      const linkText = `[@${mention.displayName}](/members/${mention.userId})`;
      
      result = beforeMention + linkText + afterMention;
    }

    return result;
  }

  /**
   * Extract user IDs from parsed mentions
   */
  static extractUserIds(mentions: ParsedMention[]): string[] {
    return mentions.map(mention => mention.userId);
  }

  /**
   * Validate mention positions don't overlap
   */
  static validateMentionPositions(mentions: ParsedMention[]): boolean {
    const sortedMentions = [...mentions].sort((a, b) => a.startPosition - b.startPosition);
    
    for (let i = 0; i < sortedMentions.length - 1; i++) {
      const current = sortedMentions[i];
      const next = sortedMentions[i + 1];
      
      if (current.endPosition > next.startPosition) {
        return false; // Overlapping mentions
      }
    }
    
    return true;
  }

  /**
   * Clean up mention text for storage
   */
  static cleanMentionText(text: string): string {
    return text.trim().replace(/\s+/g, ' ');
  }

  /**
   * Check if a position is within any mention
   */
  static isPositionInMention(position: number, mentions: ParsedMention[]): boolean {
    return mentions.some(mention => 
      position >= mention.startPosition && position < mention.endPosition
    );
  }

  /**
   * Get mention at specific position
   */
  static getMentionAtPosition(position: number, mentions: ParsedMention[]): ParsedMention | null {
    return mentions.find(mention => 
      position >= mention.startPosition && position < mention.endPosition
    ) || null;
  }

  /**
   * Remove mentions from content (get plain text)
   */
  static removeMentions(content: string): string {
    return content.replace(this.MENTION_REGEX, '');
  }

  /**
   * Count mentions in content
   */
  static countMentions(content: string): number {
    const matches = content.match(this.MENTION_REGEX);
    return matches ? matches.length : 0;
  }

  /**
   * Validate mention format
   */
  static isValidMentionFormat(text: string): boolean {
    return this.MENTION_REGEX.test(text);
  }
}
