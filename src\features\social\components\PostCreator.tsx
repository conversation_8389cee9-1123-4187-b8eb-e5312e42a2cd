import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { X, Plus, Loader2, Send, Crown } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useSubscription } from '@/hooks/useSubscription';
import { IndustrySelector } from '@/components/industries/IndustrySelector';
import { NetZeroCategorySelector } from '@/components/netzero/NetZeroCategorySelector';
import SocialPostImageUpload from './SocialPostImageUpload';
import MentionInput from './MentionInput';
import { supabase } from '@/integrations/supabase/client';
import type { PostFormData } from '../types/social.types';
import type { MentionableUser } from '../services/userSearchService';
import { UserSearchService } from '../services/userSearchService';

interface PostCreatorProps {
  onPostCreated: (postData: PostFormData) => Promise<void>;
  className?: string;
}

interface UserProfile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  avatar_url: string | null;
}

// User profile interface for displaying avatar in post creator

// Using the same components as Profile and Business forms
// No need for custom interfaces - the selectors handle their own data

const PostCreator: React.FC<PostCreatorProps> = ({ onPostCreated, className = '' }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const { getSubscriptionStatus } = useSubscription();
  const [isExpanded, setIsExpanded] = useState(false);
  const [content, setContent] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([]);
  const [imageUrl, setImageUrl] = useState<string | undefined>();
  const [imageCloudflareKey, setImageCloudflareKey] = useState<string | undefined>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [mentionedUsers, setMentionedUsers] = useState<Map<string, MentionableUser>>(new Map());

  // Character limits based on subscription
  const subscriptionStatus = getSubscriptionStatus();
  const isPremiumUser = subscriptionStatus === 'business_premium' || subscriptionStatus === 'advertising';
  const characterLimit = isPremiumUser ? 2000 : 280;

  // Fetch user profile data
  useEffect(() => {
    if (user) {
      const fetchProfile = async () => {
        const { data, error } = await supabase
          .from('profiles')
          .select('id, first_name, last_name, avatar_url')
          .eq('id', user.id)
          .single();
        
        if (!error && data) {
          setUserProfile(data);
        }
      };
      
      fetchProfile();
    }
  }, [user]);

  // No need to load data manually - the selector components handle their own data loading

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim()) {
      toast({
        title: "Content required",
        description: "Please write something before posting.",
        variant: "destructive"
      });
      return;
    }

    if (content.length > characterLimit) {
      toast({
        title: "Post too long",
        description: isPremiumUser
          ? "Posts must be 2000 characters or less."
          : "Posts must be 280 characters or less. Upgrade to Business Premium for longer posts.",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSubmitting(true);
      
      const postData: PostFormData = {
        content: content.trim(),
        netZeroCategoryIds: selectedCategories,
        industryIds: selectedIndustries,
        imageUrl,
        imageCloudflareKey
      };

      await onPostCreated(postData);
      
      // Reset form
      setContent('');
      setSelectedCategories([]);
      setSelectedIndustries([]);
      setImageUrl(undefined);
      setImageCloudflareKey(undefined);
      setMentionedUsers(new Map());
      setIsExpanded(false);
      
      toast({
        title: "Post created",
        description: "Your post has been shared with the community."
      });
    } catch (error) {
      console.error('Error creating post:', error);
      toast({
        title: "Error",
        description: "Failed to create post. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Selection handlers for the new components
  const handleCategoryChange = (categoryIds: string[]) => {
    setSelectedCategories(categoryIds);
  };

  const handleIndustryChange = (industryIds: string[]) => {
    setSelectedIndustries(industryIds);
  };

  const handleMentionSelect = (user: MentionableUser, mentionText: string) => {
    setMentionedUsers(prev => {
      const newMap = new Map(prev);
      newMap.set(mentionText, user);
      return newMap;
    });
  };

  const testUserSearch = async () => {
    try {
      console.log('Testing user search...');

      // Test 1: Search with empty query to get all mentionable users
      console.log('Test 1: Empty query search');
      const allUsers = await UserSearchService.searchMentionableUsers('', 10);
      console.log('All mentionable users:', allUsers);

      // Test 2: Search with 'test' query
      console.log('Test 2: "test" query search');
      const testUsers = await UserSearchService.searchMentionableUsers('test', 10);
      console.log('Users matching "test":', testUsers);

      // Test 3: Check profiles table directly
      console.log('Test 3: Direct profiles query');
      const { data: profiles, error } = await supabase
        .from('profiles')
        .select('id, first_name, last_name, allow_mentions, profile_visible')
        .limit(5);

      if (error) {
        console.error('Profiles query error:', error);
      } else {
        console.log('Sample profiles:', profiles);
      }

      toast({
        title: "User Search Test Complete",
        description: `All users: ${allUsers.length}, Test users: ${testUsers.length}. Check console for details.`
      });
    } catch (error) {
      console.error('User search test failed:', error);
      toast({
        title: "User Search Test Failed",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive"
      });
    }
  };

  const handleImageChange = (url?: string, cloudflareKey?: string) => {
    setImageUrl(url);
    setImageCloudflareKey(cloudflareKey);
  };

  if (!user) {
    return null;
  }

  if (!isExpanded) {
    return (
      <Card className={`mb-6 ${className}`}>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center overflow-hidden">
              {userProfile?.avatar_url ? (
                <img
                  src={userProfile.avatar_url}
                  alt={`${userProfile.first_name || 'User'} avatar`}
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-sm font-medium">
                  {userProfile?.first_name?.[0] || user?.email?.[0]?.toUpperCase() || 'U'}
                </span>
              )}
            </div>
            <div className="flex-1">
              <Button
                variant="outline"
                className="w-full justify-start text-muted-foreground"
                onClick={() => setIsExpanded(true)}
              >
                What's on your mind about sustainability?
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`mb-6 ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center overflow-hidden">
            {userProfile?.avatar_url ? (
              <img
                src={userProfile.avatar_url}
                alt={`${userProfile.first_name || 'User'} avatar`}
                className="w-full h-full object-cover"
              />
            ) : (
              <span className="text-sm font-medium">
                {userProfile?.first_name?.[0] || user?.email?.[0]?.toUpperCase() || 'U'}
              </span>
            )}
          </div>
          <CardTitle className="text-lg">Create a Post</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Content Input */}
          <div>
            <MentionInput
              placeholder="Share your sustainability insights, ask questions, or start a discussion..."
              value={content}
              onChange={setContent}
              className="min-h-[120px] resize-none"
              maxLength={characterLimit}
              rows={5}
              onMentionSelect={handleMentionSelect}
            />
            <div className="flex justify-between items-center mt-2">
              <div className="flex items-center space-x-2">
                <span className={`text-xs ${
                  content.length > characterLimit * 0.9
                    ? 'text-destructive'
                    : content.length > characterLimit * 0.8
                      ? 'text-yellow-600'
                      : 'text-muted-foreground'
                }`}>
                  {content.length}/{characterLimit} characters
                </span>
                {!isPremiumUser && (
                  <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                    <Crown size={12} className="text-yellow-500" />
                    <span>Premium: 2000 chars</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Image Upload */}
          {user && (
            <div>
              <Label className="text-sm font-medium">Image (optional)</Label>
              <div className="mt-2">
                <SocialPostImageUpload
                  imageUrl={imageUrl}
                  onImageChange={handleImageChange}
                  userId={user.id}
                  disabled={isSubmitting}
                />
              </div>
            </div>
          )}

          {/* Net-Zero Categories Selection */}
          <div>
            <NetZeroCategorySelector
              selectedSubcategories={selectedCategories}
              onSelectionChange={handleCategoryChange}
              title="Net-Zero Categories (optional)"
              description="Tag your post with relevant net-zero categories to help others discover it."
              maxSelections={3}
              allowPrimarySelection={false}
            />
          </div>

          {/* Industry Selection */}
          <div>
            <IndustrySelector
              selectedTargetIndustryIds={selectedIndustries}
              onTargetIndustriesChange={handleIndustryChange}
              mode="multi"
              title="Industries (optional)"
              description="Tag your post with relevant industries to reach the right audience."
              multiSelectLabel="Relevant Industries"
              maxSelections={3}
              allowParentSelection={false}
            />
          </div>

          {/* Test Button (temporary) */}
          <div className="mb-4">
            <Button
              type="button"
              variant="outline"
              onClick={testUserSearch}
              size="sm"
            >
              Test User Search
            </Button>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center pt-4">
            <Button
              type="button"
              variant="ghost"
              onClick={() => {
                setIsExpanded(false);
                setContent('');
                setSelectedCategories([]);
                setSelectedIndustries([]);
                setImageUrl(undefined);
                setImageCloudflareKey(undefined);
              }}
            >
              Cancel
            </Button>
            
            <Button
              type="submit"
              disabled={!content.trim() || isSubmitting || content.length > characterLimit}
              className="min-w-[100px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 size={16} className="mr-2 animate-spin" />
                  Posting...
                </>
              ) : (
                <>
                  <Send size={16} className="mr-2" />
                  Post
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default PostCreator;
