-- Add Interest Matching System for Events and Funding
-- This migration adds net zero categories to events and funding, plus notification preferences

-- Add net zero categories to events
CREATE TABLE IF NOT EXISTS public.event_netzero_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id UUID NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
    subcategory_id UUID NOT NULL REFERENCES public.netzero_subcategories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(event_id, subcategory_id)
);

-- Add net zero categories to funding opportunities
CREATE TABLE IF NOT EXISTS public.funding_netzero_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    funding_opportunity_id UUID NOT NULL REFERENCES public.funding_opportunities(id) ON DELETE CASCADE,
    subcategory_id UUID NOT NULL REFERENCES public.netzero_subcategories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(funding_opportunity_id, subcategory_id)
);

-- Add industry to events (optional)
ALTER TABLE public.events 
ADD COLUMN target_industry_id UUID REFERENCES public.uk_industries(id);

-- Add industry to funding opportunities (optional)
ALTER TABLE public.funding_opportunities 
ADD COLUMN target_industry_id UUID REFERENCES public.uk_industries(id);

-- Add notification preferences to profiles
ALTER TABLE public.profiles 
ADD COLUMN notify_interest_matches BOOLEAN DEFAULT true,
ADD COLUMN notify_new_events BOOLEAN DEFAULT true,
ADD COLUMN notify_new_funding BOOLEAN DEFAULT true,
ADD COLUMN notify_new_businesses BOOLEAN DEFAULT true;

-- Add new notification types to enum
ALTER TYPE notification_type_enum ADD VALUE 'event_interest_match';
ALTER TYPE notification_type_enum ADD VALUE 'funding_interest_match';
ALTER TYPE notification_type_enum ADD VALUE 'business_interest_match';

-- Create indexes for performance
CREATE INDEX idx_event_netzero_categories_event_id ON public.event_netzero_categories(event_id);
CREATE INDEX idx_event_netzero_categories_subcategory_id ON public.event_netzero_categories(subcategory_id);

CREATE INDEX idx_funding_netzero_categories_funding_id ON public.funding_netzero_categories(funding_opportunity_id);
CREATE INDEX idx_funding_netzero_categories_subcategory_id ON public.funding_netzero_categories(subcategory_id);

CREATE INDEX idx_events_target_industry_id ON public.events(target_industry_id);
CREATE INDEX idx_funding_target_industry_id ON public.funding_opportunities(target_industry_id);

-- Function to find users interested in specific net zero categories
CREATE OR REPLACE FUNCTION get_users_interested_in_categories(category_ids UUID[])
RETURNS TABLE (
    user_id UUID,
    first_name TEXT,
    last_name TEXT,
    notify_interest_matches BOOLEAN,
    notify_new_events BOOLEAN,
    notify_new_funding BOOLEAN,
    notify_new_businesses BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT
        p.id as user_id,
        p.first_name,
        p.last_name,
        p.notify_interest_matches,
        p.notify_new_events,
        p.notify_new_funding,
        p.notify_new_businesses
    FROM public.profiles p
    JOIN public.profile_netzero_interests pni ON p.id = pni.profile_id
    WHERE 
        pni.subcategory_id = ANY(category_ids)
        AND p.profile_visible = true
        AND p.notify_interest_matches = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to find users interested in specific industry
CREATE OR REPLACE FUNCTION get_users_interested_in_industry(industry_id UUID)
RETURNS TABLE (
    user_id UUID,
    first_name TEXT,
    last_name TEXT,
    notify_interest_matches BOOLEAN,
    notify_new_events BOOLEAN,
    notify_new_funding BOOLEAN,
    notify_new_businesses BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id as user_id,
        p.first_name,
        p.last_name,
        p.notify_interest_matches,
        p.notify_new_events,
        p.notify_new_funding,
        p.notify_new_businesses
    FROM public.profiles p
    WHERE 
        p.main_industry_id = industry_id
        AND p.profile_visible = true
        AND p.notify_interest_matches = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to notify users about new events matching their interests
CREATE OR REPLACE FUNCTION notify_users_about_new_event()
RETURNS TRIGGER AS $$
DECLARE
    interested_user RECORD;
    event_categories UUID[];
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
BEGIN
    -- Get event categories
    SELECT ARRAY_AGG(subcategory_id) INTO event_categories
    FROM public.event_netzero_categories
    WHERE event_id = NEW.id;

    -- Create notification content
    notification_title := 'New event in your area of interest';
    notification_message := 'A new event "' || NEW.name || '" has been added that matches your interests.';
    action_url := '/events/' || NEW.id::TEXT;
    notification_data := jsonb_build_object(
        'event_id', NEW.id,
        'event_name', NEW.name,
        'event_start_date', NEW.start_date,
        'event_location_type', NEW.location_type
    );

    -- Notify users interested in the event categories
    IF event_categories IS NOT NULL AND array_length(event_categories, 1) > 0 THEN
        FOR interested_user IN 
            SELECT * FROM get_users_interested_in_categories(event_categories)
            WHERE notify_new_events = true
        LOOP
            -- Create notification
            PERFORM create_notification(
                interested_user.user_id,
                'event_interest_match'::notification_type_enum,
                notification_title,
                notification_message,
                NEW.id,
                'event',
                action_url,
                notification_data
            );
        END LOOP;
    END IF;

    -- Notify users interested in the target industry
    IF NEW.target_industry_id IS NOT NULL THEN
        FOR interested_user IN 
            SELECT * FROM get_users_interested_in_industry(NEW.target_industry_id)
            WHERE notify_new_events = true
        LOOP
            -- Create notification
            PERFORM create_notification(
                interested_user.user_id,
                'event_interest_match'::notification_type_enum,
                notification_title,
                notification_message,
                NEW.id,
                'event',
                action_url,
                notification_data
            );
        END LOOP;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to notify users about new funding matching their interests
CREATE OR REPLACE FUNCTION notify_users_about_new_funding()
RETURNS TRIGGER AS $$
DECLARE
    interested_user RECORD;
    funding_categories UUID[];
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
BEGIN
    -- Get funding categories
    SELECT ARRAY_AGG(subcategory_id) INTO funding_categories
    FROM public.funding_netzero_categories
    WHERE funding_opportunity_id = NEW.id;

    -- Create notification content
    notification_title := 'New funding opportunity in your area of interest';
    notification_message := 'A new funding opportunity "' || NEW.name || '" has been added that matches your interests.';
    action_url := '/funding/' || NEW.id::TEXT;
    notification_data := jsonb_build_object(
        'funding_id', NEW.id,
        'funding_name', NEW.name,
        'funding_type', NEW.funding_type,
        'organization_name', NEW.organization_name,
        'deadline_date', NEW.deadline_date
    );

    -- Notify users interested in the funding categories
    IF funding_categories IS NOT NULL AND array_length(funding_categories, 1) > 0 THEN
        FOR interested_user IN 
            SELECT * FROM get_users_interested_in_categories(funding_categories)
            WHERE notify_new_funding = true
        LOOP
            -- Create notification
            PERFORM create_notification(
                interested_user.user_id,
                'funding_interest_match'::notification_type_enum,
                notification_title,
                notification_message,
                NEW.id,
                'funding_opportunity',
                action_url,
                notification_data
            );
        END LOOP;
    END IF;

    -- Notify users interested in the target industry
    IF NEW.target_industry_id IS NOT NULL THEN
        FOR interested_user IN 
            SELECT * FROM get_users_interested_in_industry(NEW.target_industry_id)
            WHERE notify_new_funding = true
        LOOP
            -- Create notification
            PERFORM create_notification(
                interested_user.user_id,
                'funding_interest_match'::notification_type_enum,
                notification_title,
                notification_message,
                NEW.id,
                'funding_opportunity',
                action_url,
                notification_data
            );
        END LOOP;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to notify users about new businesses matching their interests
CREATE OR REPLACE FUNCTION notify_users_about_new_business()
RETURNS TRIGGER AS $$
DECLARE
    interested_user RECORD;
    business_categories UUID[];
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
BEGIN
    -- Get business categories
    SELECT ARRAY_AGG(subcategory_id) INTO business_categories
    FROM public.business_netzero_categories
    WHERE business_id = NEW.id;

    -- Create notification content
    notification_title := 'New business in your area of interest';
    notification_message := 'A new business "' || NEW.business_name || '" has been added that matches your interests.';
    action_url := '/businesses/' || NEW.id::TEXT;
    notification_data := jsonb_build_object(
        'business_id', NEW.id,
        'business_name', NEW.business_name
    );

    -- Notify users interested in the business categories
    IF business_categories IS NOT NULL AND array_length(business_categories, 1) > 0 THEN
        FOR interested_user IN 
            SELECT * FROM get_users_interested_in_categories(business_categories)
            WHERE notify_new_businesses = true
        LOOP
            -- Create notification
            PERFORM create_notification(
                interested_user.user_id,
                'business_interest_match'::notification_type_enum,
                notification_title,
                notification_message,
                NEW.id,
                'business',
                action_url,
                notification_data
            );
        END LOOP;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for interest matching notifications
CREATE TRIGGER trigger_notify_new_event_interests
    AFTER INSERT ON public.events
    FOR EACH ROW
    EXECUTE FUNCTION notify_users_about_new_event();

CREATE TRIGGER trigger_notify_new_funding_interests
    AFTER INSERT ON public.funding_opportunities
    FOR EACH ROW
    EXECUTE FUNCTION notify_users_about_new_funding();

CREATE TRIGGER trigger_notify_new_business_interests
    AFTER INSERT ON public.businesses
    FOR EACH ROW
    EXECUTE FUNCTION notify_users_about_new_business();

-- Grant permissions
GRANT ALL ON TABLE public.event_netzero_categories TO authenticated;
GRANT ALL ON TABLE public.funding_netzero_categories TO authenticated;
GRANT ALL ON TABLE public.event_netzero_categories TO service_role;
GRANT ALL ON TABLE public.funding_netzero_categories TO service_role;

GRANT EXECUTE ON FUNCTION get_users_interested_in_categories(UUID[]) TO authenticated;
GRANT EXECUTE ON FUNCTION get_users_interested_in_industry(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_users_interested_in_categories(UUID[]) TO service_role;
GRANT EXECUTE ON FUNCTION get_users_interested_in_industry(UUID) TO service_role;

-- Add comments for documentation
COMMENT ON TABLE public.event_netzero_categories IS 'Net zero categories for events to match user interests';
COMMENT ON TABLE public.funding_netzero_categories IS 'Net zero categories for funding opportunities to match user interests';
COMMENT ON COLUMN public.events.target_industry_id IS 'Target industry for the event (optional)';
COMMENT ON COLUMN public.funding_opportunities.target_industry_id IS 'Target industry for the funding opportunity (optional)';
COMMENT ON COLUMN public.profiles.notify_interest_matches IS 'Whether user wants notifications for content matching their interests';
COMMENT ON COLUMN public.profiles.notify_new_events IS 'Whether user wants notifications for new events';
COMMENT ON COLUMN public.profiles.notify_new_funding IS 'Whether user wants notifications for new funding opportunities';
COMMENT ON COLUMN public.profiles.notify_new_businesses IS 'Whether user wants notifications for new businesses';
