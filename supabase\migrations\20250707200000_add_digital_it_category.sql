-- Add Digital / IT category and subcategories to the netzero categories system
-- This migration adds a new category for digital transformation and IT infrastructure solutions

-- Insert new Digital / IT category
INSERT INTO "public"."netzero_categories" ("id", "name", "description", "sort_order", "created_at", "updated_at") 
VALUES ('a8f4c2b5-9e1d-4a7c-b3f8-2c5e7d9a4b6c', 'Digital / IT', 'Digital transformation and IT infrastructure for net-zero emissions', '8', NOW(), NOW());

-- Insert subcategories for Digital / IT
INSERT INTO "public"."netzero_subcategories" ("id", "category_id", "name", "description", "sort_order", "created_at", "updated_at") 
VALUES 
('b7e3f1a8-4c9d-4e2b-a5f6-3d8c1e7b9a2f', 'a8f4c2b5-9e1d-4a7c-b3f8-2c5e7d9a4b6c', 'Green Data Centers', 'Energy-efficient data centers and cloud infrastructure optimization', '1', NOW(), NOW()),
('c9d2e4f7-6b8a-4f3c-9e1d-5a7b2c4e8f9a', 'a8f4c2b5-9e1d-4a7c-b3f8-2c5e7d9a4b6c', 'Digital Carbon Management', 'IoT sensors, AI-powered monitoring, and carbon tracking systems', '2', NOW(), NOW()),
('d1f5e8b3-7c9a-4d2e-b6f4-8a3c5e7d9b1f', 'a8f4c2b5-9e1d-4a7c-b3f8-2c5e7d9a4b6c', 'Remote Work Solutions', 'Digital collaboration tools and virtual meeting platforms', '3', NOW(), NOW()),
('e4a7b2c8-9d1f-4e5a-c7b3-6e8f1a4c7d9b', 'a8f4c2b5-9e1d-4a7c-b3f8-2c5e7d9a4b6c', 'Smart Grid and Energy Management', 'Smart meters, grid optimization, and energy management software', '4', NOW(), NOW()),
('f8c1d5e9-3a7b-4f2c-d8e1-9b5c2f6a8d4e', 'a8f4c2b5-9e1d-4a7c-b3f8-2c5e7d9a4b6c', 'Digital Twin Technology', 'Virtual modeling for emissions optimization and predictive analytics', '5', NOW(), NOW()),
('a2e6f9c4-5b8d-4a3e-f7c2-1d9b4e6a8c5f', 'a8f4c2b5-9e1d-4a7c-b3f8-2c5e7d9a4b6c', 'Sustainable Software Development', 'Green coding practices and energy-efficient algorithms', '6', NOW(), NOW());

-- Add Sustainability and Net Zero Consulting to UK industries under Professional Services
-- Find the Professional Services parent industry and add the subcategory
DO $$
DECLARE
    professional_services_id UUID;
BEGIN
    -- Find Professional Services industry ID
    SELECT id INTO professional_services_id
    FROM public.uk_industries
    WHERE name ILIKE '%Professional%Services%' OR name ILIKE '%Professional%'
    LIMIT 1;

    -- Only insert if we found a Professional Services category
    IF professional_services_id IS NOT NULL THEN
        INSERT INTO "public"."uk_industries" ("id", "parent_id", "name", "description", "sort_order", "created_at", "updated_at")
        VALUES (gen_random_uuid(), professional_services_id, 'Sustainability and Net Zero Consulting', 'Environmental consulting, carbon management advisory, net-zero strategy development and ESG compliance services', '8', NOW(), NOW())
        ON CONFLICT (name) DO NOTHING;
    END IF;
END $$;