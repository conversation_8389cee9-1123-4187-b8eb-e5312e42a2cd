import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';

// Interface for mention data from database
export interface MentionData {
  id: string;
  mentioned_user_id: string;
  mention_text: string;
  position_start: number;
  position_end: number;
  mentioned_user?: {
    id: string;
    first_name: string | null;
    last_name: string | null;
    job_title: string | null;
    organisation_name: string | null;
    avatar_url: string | null;
    profile_visible: boolean;
  };
}

interface MentionDisplayProps {
  content: string;
  mentions?: MentionData[];
  className?: string;
}

interface ProcessedContent {
  type: 'text' | 'mention';
  content: string;
  mentionData?: MentionData;
}

const MentionDisplay: React.FC<MentionDisplayProps> = ({
  content,
  mentions = [],
  className
}) => {
  // Process content to identify mentions and regular text
  const processContent = (): ProcessedContent[] => {
    if (!mentions || mentions.length === 0) {
      return [{ type: 'text', content }];
    }

    // Sort mentions by position
    const sortedMentions = [...mentions].sort((a, b) => a.position_start - b.position_start);
    
    const processed: ProcessedContent[] = [];
    let currentPosition = 0;

    for (const mention of sortedMentions) {
      // Add text before mention
      if (mention.position_start > currentPosition) {
        const textBefore = content.substring(currentPosition, mention.position_start);
        if (textBefore) {
          processed.push({ type: 'text', content: textBefore });
        }
      }

      // Add mention
      processed.push({
        type: 'mention',
        content: mention.mention_text,
        mentionData: mention
      });

      currentPosition = mention.position_end;
    }

    // Add remaining text after last mention
    if (currentPosition < content.length) {
      const textAfter = content.substring(currentPosition);
      if (textAfter) {
        processed.push({ type: 'text', content: textAfter });
      }
    }

    return processed;
  };

  // Format display name for mentioned user
  const formatMentionDisplayName = (mention: MentionData): string => {
    if (!mention.mentioned_user) {
      return mention.mention_text;
    }

    const user = mention.mentioned_user;
    if (user.first_name && user.last_name) {
      return `@${user.first_name} ${user.last_name}`;
    }
    return `@${user.first_name || user.last_name || 'Unknown User'}`;
  };

  // Check if user profile is accessible
  const isUserProfileAccessible = (mention: MentionData): boolean => {
    return mention.mentioned_user?.profile_visible === true;
  };

  const processedContent = processContent();

  return (
    <div className={cn("whitespace-pre-wrap break-words", className)}>
      {processedContent.map((item, index) => {
        if (item.type === 'text') {
          return <span key={index}>{item.content}</span>;
        }

        // Handle mention
        const mention = item.mentionData!;
        const displayName = formatMentionDisplayName(mention);
        const isAccessible = isUserProfileAccessible(mention);

        if (isAccessible) {
          return (
            <Link
              key={index}
              to={`/members/${mention.mentioned_user_id}`}
              className={cn(
                "font-medium text-primary hover:text-primary/80 hover:underline",
                "transition-colors duration-200"
              )}
              title={`View ${mention.mentioned_user?.first_name || 'user'}'s profile`}
            >
              {displayName}
            </Link>
          );
        } else {
          // User profile not accessible, show as plain text
          return (
            <span
              key={index}
              className="font-medium text-muted-foreground"
              title="User profile not available"
            >
              {displayName}
            </span>
          );
        }
      })}
    </div>
  );
};

// Alternative component for simple mention rendering without database data
interface SimpleMentionDisplayProps {
  content: string;
  className?: string;
}

export const SimpleMentionDisplay: React.FC<SimpleMentionDisplayProps> = ({
  content,
  className
}) => {
  // Simple regex-based mention detection for display only
  const mentionRegex = /@\[([^\]]+)\]\(\/members\/([^)]+)\)/g;
  
  const processContent = () => {
    const parts: React.ReactNode[] = [];
    let lastIndex = 0;
    let match;

    while ((match = mentionRegex.exec(content)) !== null) {
      // Add text before mention
      if (match.index > lastIndex) {
        parts.push(content.substring(lastIndex, match.index));
      }

      // Add mention link
      const displayName = match[1];
      const userId = match[2];
      
      parts.push(
        <Link
          key={match.index}
          to={`/members/${userId}`}
          className={cn(
            "font-medium text-primary hover:text-primary/80 hover:underline",
            "transition-colors duration-200"
          )}
          title={`View ${displayName}'s profile`}
        >
          @{displayName}
        </Link>
      );

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < content.length) {
      parts.push(content.substring(lastIndex));
    }

    return parts;
  };

  return (
    <div className={cn("whitespace-pre-wrap break-words", className)}>
      {processContent()}
    </div>
  );
};

export default MentionDisplay;
