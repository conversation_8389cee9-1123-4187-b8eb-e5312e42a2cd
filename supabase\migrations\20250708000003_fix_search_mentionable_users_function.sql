-- Fix search_mentionable_users function to handle empty queries
-- This migration updates the function to return all mentionable users when search_query is empty

CREATE OR REPLACE FUNCTION search_mentionable_users(search_query TEXT, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
    id UUID,
    first_name TEXT,
    last_name TEXT,
    job_title TEXT,
    organisation_name TEXT,
    avatar_url TEXT,
    display_name TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.first_name,
        p.last_name,
        p.job_title,
        p.organisation_name,
        p.avatar_url,
        CASE 
            WHEN p.first_name IS NOT NULL AND p.last_name IS NOT NULL THEN 
                p.first_name || ' ' || p.last_name
            WHEN p.first_name IS NOT NULL THEN 
                p.first_name
            WHEN p.last_name IS NOT NULL THEN 
                p.last_name
            ELSE 
                'Anonymous User'
        END as display_name
    FROM public.profiles p
    WHERE 
        p.profile_visible = true 
        AND p.allow_mentions = true
        AND (
            search_query = '' OR search_query IS NULL OR
            p.first_name ILIKE '%' || search_query || '%' 
            OR p.last_name ILIKE '%' || search_query || '%'
            OR (p.first_name || ' ' || p.last_name) ILIKE '%' || search_query || '%'
            OR p.organisation_name ILIKE '%' || search_query || '%'
        )
    ORDER BY 
        -- Prioritize exact matches
        CASE 
            WHEN search_query = '' OR search_query IS NULL THEN 1
            WHEN (p.first_name || ' ' || p.last_name) ILIKE search_query || '%' THEN 1
            WHEN p.first_name ILIKE search_query || '%' THEN 2
            WHEN p.last_name ILIKE search_query || '%' THEN 3
            ELSE 4
        END,
        p.first_name, p.last_name
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
