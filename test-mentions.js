// Simple test script to debug mention functionality
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testMentions() {
  console.log('Testing mention functionality...');
  
  // 1. Check if there are any users in the profiles table
  console.log('\n1. Checking profiles table...');
  const { data: profiles, error: profilesError } = await supabase
    .from('profiles')
    .select('id, first_name, last_name, allow_mentions, profile_visible')
    .limit(5);
  
  if (profilesError) {
    console.error('Error fetching profiles:', profilesError);
  } else {
    console.log('Found profiles:', profiles);
  }
  
  // 2. Test the search_mentionable_users function
  console.log('\n2. Testing search_mentionable_users function...');
  const { data: searchResult, error: searchError } = await supabase
    .rpc('search_mentionable_users', {
      search_query: 'test',
      limit_count: 10
    });
  
  if (searchError) {
    console.error('Error calling search_mentionable_users:', searchError);
  } else {
    console.log('Search result:', searchResult);
  }
  
  // 3. Test with a broader search
  console.log('\n3. Testing with broader search...');
  const { data: broadSearch, error: broadError } = await supabase
    .rpc('search_mentionable_users', {
      search_query: '',
      limit_count: 10
    });
  
  if (broadError) {
    console.error('Error with broad search:', broadError);
  } else {
    console.log('Broad search result:', broadSearch);
  }
  
  // 4. Check if allow_mentions column exists and has correct values
  console.log('\n4. Checking allow_mentions column...');
  const { data: mentionSettings, error: mentionError } = await supabase
    .from('profiles')
    .select('id, first_name, last_name, allow_mentions, profile_visible')
    .not('first_name', 'is', null)
    .limit(10);
  
  if (mentionError) {
    console.error('Error checking mention settings:', mentionError);
  } else {
    console.log('Users with mention settings:', mentionSettings);
  }
}

testMentions().catch(console.error);
