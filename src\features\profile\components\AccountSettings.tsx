import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { CloudflareService } from '@/services/cloudflareService';

interface AccountSettingsProps {
  userId: string;
  signOut: () => Promise<void>;
  onSettingsUpdated?: () => void;
}

interface UserSettings {
  profile_visible: boolean;
  newsletter_subscribed: boolean;
  show_business_menu: boolean;
  can_add_funding_opportunities: boolean;
  can_add_events: boolean;
  allow_mentions: boolean;
}

const AccountSettings = ({ userId, signOut, onSettingsUpdated }: AccountSettingsProps) => {
  const [loading, setLoading] = useState(false);
  const [settingsLoading, setSettingsLoading] = useState(true);
  const [settings, setSettings] = useState<UserSettings>({
    profile_visible: true,
    newsletter_subscribed: false,
    show_business_menu: true,
    can_add_funding_opportunities: true,
    can_add_events: true,
    allow_mentions: true,
  });
  const { toast } = useToast();
  const navigate = useNavigate();
  const cloudflareService = new CloudflareService();

  // Fetch current user settings
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('profile_visible, newsletter_subscribed, show_business_menu, can_add_funding_opportunities, can_add_events, allow_mentions')
          .eq('id', userId)
          .single();

        if (error) throw error;

        if (data) {
          setSettings({
            // @ts-ignore - Types will be updated after running supabase gen types
            profile_visible: data.profile_visible ?? true,
            // @ts-ignore - Types will be updated after running supabase gen types
            newsletter_subscribed: data.newsletter_subscribed ?? false,
            // @ts-ignore - Types will be updated after running supabase gen types
            show_business_menu: data.show_business_menu ?? true,
            // @ts-ignore - Types will be updated after running supabase gen types
            can_add_funding_opportunities: data.can_add_funding_opportunities ?? true,
            // @ts-ignore - Types will be updated after running supabase gen types
            can_add_events: data.can_add_events ?? true,
          });
        }
      } catch (error) {
        console.error('Error fetching user settings:', error);
        toast({
          title: "Error",
          description: "Failed to load account settings",
          variant: "destructive"
        });
      } finally {
        setSettingsLoading(false);
      }
    };

    fetchSettings();
  }, [userId, toast]);

  // Update a specific setting
  const updateSetting = async (key: keyof UserSettings, value: boolean) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ [key]: value })
        .eq('id', userId);

      if (error) throw error;

      setSettings(prev => ({ ...prev, [key]: value }));
      
      // Notify parent component that settings were updated
      if (onSettingsUpdated) {
        onSettingsUpdated();
      }
      
      toast({
        title: "Settings Updated",
        description: "Your account settings have been saved",
      });
    } catch (error) {
      console.error('Error updating setting:', error);
      toast({
        title: "Error",
        description: "Failed to update setting. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleDeleteAccount = async () => {
    const confirmed = window.confirm(
      'Are you sure you want to delete your account? This action cannot be undone.'
    );
    
    if (confirmed) {
      setLoading(true);
      
      try {
        // First, get the user's profile to check for avatar
        const { data: profile, error: profileFetchError } = await supabase
          .from('profiles')
          .select('avatar_cloudflare_key')
          .eq('id', userId)
          .single();

        if (profileFetchError) {
          console.error('Error fetching profile for deletion:', profileFetchError);
        }

        // Delete avatar from Cloudflare R2 if it exists
        // @ts-ignore - Types will be updated after running supabase gen types
        if (profile?.avatar_cloudflare_key) {
          try {
            // @ts-ignore - Types will be updated after running supabase gen types
            await cloudflareService.deleteAvatar(profile.avatar_cloudflare_key);
            console.log('Avatar deleted from Cloudflare R2');
          } catch (avatarError) {
            console.error('Error deleting avatar from Cloudflare R2:', avatarError);
            // Continue with account deletion even if avatar deletion fails
          }
        }

        // Method 1: Try using edge function (if deployed)
        const session = await supabase.auth.getSession();
        if (session.data.session) {
          const { data, error: functionError } = await supabase.functions.invoke('delete-user', {
            headers: {
              Authorization: `Bearer ${session.data.session.access_token}`
            }
          });
          
          if (!functionError) {
            toast({
              title: "Account Deleted",
              description: "Your account has been permanently deleted."
            });
            await signOut();
            return;
          }
        }
        
        // Method 2: Fallback - delete profile data and sign out
        console.log('Edge function not available, using fallback method');
        
        const { error: profileError } = await supabase
          .from('profiles')
          .delete()
          .eq('id', userId);
        
        if (profileError) {
          throw profileError;
        }
        
        await signOut();
        
        toast({
          title: "Account Data Deleted",
          description: "Your profile data has been deleted and you have been signed out. Please contact support to complete account deletion."
        });
        
      } catch (error) {
        console.error('Error deleting account:', error);
        toast({
          title: "Error",
          description: "Failed to delete account. Please contact support.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
      toast({
        title: "Error",
        description: "Failed to sign out. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* User Preferences */}
      <Card>
        <CardHeader>
          <CardTitle>Privacy & Preferences</CardTitle>
          <CardDescription>
            Manage your account visibility and communication preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {settingsLoading ? (
            <div className="text-sm text-muted-foreground">Loading settings...</div>
          ) : (
            <>
              {/* Profile Visibility */}
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="profile-visible" className="text-base">
                    Public Profile
                  </Label>
                  <div className="text-sm text-muted-foreground">
                    Show your profile in the public member directory
                  </div>
                </div>
                <Switch
                  id="profile-visible"
                  checked={settings.profile_visible}
                  onCheckedChange={(checked) => updateSetting('profile_visible', checked)}
                />
              </div>

              {/* Newsletter Subscription */}
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="newsletter-subscribed" className="text-base">
                    Newsletter Subscription
                  </Label>
                  <div className="text-sm text-muted-foreground">
                    Receive updates about platform features and sustainability news
                  </div>
                </div>
                <Switch
                  id="newsletter-subscribed"
                  checked={settings.newsletter_subscribed}
                  onCheckedChange={(checked) => updateSetting('newsletter_subscribed', checked)}
                />
              </div>

              {/* Business Menu Visibility */}
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="show-business-menu" className="text-base">
                    Businesses
                  </Label>
                  <div className="text-sm text-muted-foreground">
                    Show businesses option in your profile sidebar to add and track business directory entries
                  </div>
                </div>
                <Switch
                  id="show-business-menu"
                  checked={settings.show_business_menu}
                  onCheckedChange={(checked) => updateSetting('show_business_menu', checked)}
                />
              </div>

              {/* Funding Opportunities Management */}
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="can-add-funding-opportunities" className="text-base">
                    Funding Opportunities
                  </Label>
                  <div className="text-sm text-muted-foreground">
                    Allow yourself to add funding opportunities to the directory and track your interests & collaboration opportunities
                  </div>
                </div>
                <Switch
                  id="can-add-funding-opportunities"
                  checked={settings.can_add_funding_opportunities}
                  onCheckedChange={(checked) => updateSetting('can_add_funding_opportunities', checked)}
                />
              </div>

              {/* Events Management */}
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="can-add-events" className="text-base">
                    Events
                  </Label>
                  <div className="text-sm text-muted-foreground">
                    Allow yourself to add and manage events in the directory
                  </div>
                </div>
                <Switch
                  id="can-add-events"
                  checked={settings.can_add_events}
                  onCheckedChange={(checked) => updateSetting('can_add_events', checked)}
                />
              </div>

              {/* Allow Mentions */}
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="allow-mentions" className="text-base">
                    Allow Mentions
                  </Label>
                  <div className="text-sm text-muted-foreground">
                    Allow other users to mention you in social posts and comments
                  </div>
                </div>
                <Switch
                  id="allow-mentions"
                  checked={settings.allow_mentions}
                  onCheckedChange={(checked) => updateSetting('allow_mentions', checked)}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Account Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Account Actions</CardTitle>
          <CardDescription>
            Manage your account and data
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <Button 
            variant="outline" 
            onClick={handleSignOut} 
            className="w-full"
          >
            Sign Out
          </Button>
          
          <Button 
            variant="destructive" 
            onClick={handleDeleteAccount}
            className="w-full"
            disabled={loading}
          >
            {loading ? "Deleting..." : "Delete Account"}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default AccountSettings;
