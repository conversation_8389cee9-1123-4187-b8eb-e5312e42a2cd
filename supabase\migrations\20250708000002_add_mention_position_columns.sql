-- Add position columns to mention tables
-- This migration adds the missing position_start and position_end columns

-- Add position columns to social_post_mentions
ALTER TABLE public.social_post_mentions 
ADD COLUMN IF NOT EXISTS position_start INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS position_end INTEGER DEFAULT 0;

-- Add position columns to social_comment_mentions  
ALTER TABLE public.social_comment_mentions 
ADD COLUMN IF NOT EXISTS position_start INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS position_end INTEGER DEFAULT 0;

-- Make the columns NOT NULL after adding them
ALTER TABLE public.social_post_mentions 
ALTER COLUMN position_start SET NOT NULL,
ALTER COLUMN position_end SET NOT NULL;

ALTER TABLE public.social_comment_mentions 
ALTER COLUMN position_start SET NOT NULL,
ALTER COLUMN position_end SET NOT NULL;

-- Add comments for documentation
COMMENT ON COLUMN public.social_post_mentions.position_start IS 'Start position of mention in post content';
COMMENT ON COLUMN public.social_post_mentions.position_end IS 'End position of mention in post content';
COMMENT ON COLUMN public.social_comment_mentions.position_start IS 'Start position of mention in comment content';
COMMENT ON COLUMN public.social_comment_mentions.position_end IS 'End position of mention in comment content';
