import { supabase } from '@/integrations/supabase/client'
import type {
  FundingOpportunity,
  NewFundingOpportunity,
  FundingOpportunityWithInterests,
  FundingOpportunityFilters,
  FundingOpportunitySortOptions,
  FundingOpportunityListResponse
} from '../types'

export class FundingOpportunityService {
  /**
   * Get all funding opportunities with optional filtering and pagination
   */
  static async getFundingOpportunities(
    filters: FundingOpportunityFilters = {},
    sort: FundingOpportunitySortOptions = { field: 'date_listed', direction: 'desc' },
    page: number = 1,
    limit: number = 20
  ): Promise<FundingOpportunityListResponse> {
    let query = supabase
      .from('funding_opportunities')
      .select(`
        *,
        creator:created_by_user_id (
          id,
          first_name,
          last_name,
          avatar_url
        ),
        interests:funding_opportunity_interests (
          id,
          user_id,
          is_interested,
          wants_collaboration,
          note,
          created_at
        )
      `)

    // Apply filters
    if (filters.funding_type && filters.funding_type.length > 0) {
      query = query.in('funding_type', filters.funding_type)
    }

    if (filters.organization_name) {
      query = query.ilike('organization_name', `%${filters.organization_name}%`)
    }

    if (filters.deadline_after) {
      query = query.gte('deadline_date', filters.deadline_after)
    }

    if (filters.deadline_before) {
      query = query.lte('deadline_date', filters.deadline_before)
    }

    if (filters.has_deadline !== undefined) {
      if (filters.has_deadline) {
        query = query.not('deadline_date', 'is', null)
      } else {
        query = query.is('deadline_date', null)
      }
    }

    if (filters.search_query) {
      query = query.or(`name.ilike.%${filters.search_query}%,description.ilike.%${filters.search_query}%,organization_name.ilike.%${filters.search_query}%`)
    }

    // Apply sorting
    query = query.order(sort.field, { ascending: sort.direction === 'asc' })

    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      throw new Error(`Failed to fetch funding opportunities: ${error.message}`)
    }

    // Transform data to include computed fields
    const transformedData: FundingOpportunityWithInterests[] = (data || []).map(item => ({
      ...item,
      creator: item.creator,
      interests: item.interests || [],
      interest_count: item.interests?.filter(i => i.is_interested).length || 0,
      collaboration_count: item.interests?.filter(i => i.wants_collaboration).length || 0,
      user_interest: null // Will be set by getUserInterest if needed
    }))

    return {
      data: transformedData,
      count: count || 0,
      page,
      limit,
      total_pages: Math.ceil((count || 0) / limit)
    }
  }

  /**
   * Get a single funding opportunity by ID
   */
  static async getFundingOpportunityById(id: string): Promise<FundingOpportunityWithInterests | null> {
    const { data, error } = await supabase
      .from('funding_opportunities')
      .select(`
        *,
        creator:created_by_user_id (
          id,
          first_name,
          last_name,
          avatar_url
        ),
        interests:funding_opportunity_interests (
          id,
          user_id,
          is_interested,
          wants_collaboration,
          note,
          created_at,
          user:user_id (
            id,
            first_name,
            last_name,
            avatar_url
          )
        )
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch funding opportunity: ${error.message}`)
    }

    // Get current user's interest
    let userInterest = null
    try {
      userInterest = await this.getUserInterest(id)
    } catch (err) {
      // Don't fail if we can't get user interest
      console.error('Error fetching user interest:', err)
    }

    return {
      ...data,
      creator: data.creator,
      interests: data.interests || [],
      interest_count: data.interests?.filter(i => i.is_interested).length || 0,
      collaboration_count: data.interests?.filter(i => i.wants_collaboration).length || 0,
      user_interest: userInterest
    }
  }

  /**
   * Create a new funding opportunity
   */
  static async createFundingOpportunity(
    fundingData: Omit<NewFundingOpportunity, 'created_by_user_id'> & { net_zero_categories?: string[] }
  ): Promise<FundingOpportunity> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User must be authenticated to create funding opportunities')
    }

    // Extract net zero categories from fundingData
    const { net_zero_categories, ...fundingDataWithoutCategories } = fundingData

    const { data, error } = await supabase
      .from('funding_opportunities')
      .insert({
        ...fundingDataWithoutCategories,
        created_by_user_id: user.id
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create funding opportunity: ${error.message}`)
    }

    // Add net zero categories if provided
    if (net_zero_categories && net_zero_categories.length > 0) {
      await this.updateFundingCategories(data.id, net_zero_categories)
    }

    return data
  }

  /**
   * Update funding opportunity net zero categories
   */
  static async updateFundingCategories(fundingId: string, categoryIds: string[]): Promise<void> {
    // First, remove existing categories
    const { error: deleteError } = await supabase
      .from('funding_netzero_categories')
      .delete()
      .eq('funding_opportunity_id', fundingId)

    if (deleteError) {
      throw new Error(`Failed to remove existing categories: ${deleteError.message}`)
    }

    // Then, add new categories
    if (categoryIds.length > 0) {
      const categoryData = categoryIds.map(subcategoryId => ({
        funding_opportunity_id: fundingId,
        subcategory_id: subcategoryId
      }))

      const { error: insertError } = await supabase
        .from('funding_netzero_categories')
        .insert(categoryData)

      if (insertError) {
        throw new Error(`Failed to add new categories: ${insertError.message}`)
      }
    }
  }

  /**
   * Update a funding opportunity
   */
  static async updateFundingOpportunity(
    id: string,
    updates: Partial<Omit<NewFundingOpportunity, 'created_by_user_id'>>
  ): Promise<FundingOpportunity> {
    const { data, error } = await supabase
      .from('funding_opportunities')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update funding opportunity: ${error.message}`)
    }

    return data
  }

  /**
   * Delete a funding opportunity
   */
  static async deleteFundingOpportunity(id: string): Promise<void> {
    const { error } = await supabase
      .from('funding_opportunities')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete funding opportunity: ${error.message}`)
    }
  }

  /**
   * Get user's interest in a specific funding opportunity
   */
  static async getUserInterest(fundingOpportunityId: string): Promise<any> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return null
    }

    const { data, error } = await supabase
      .from('funding_opportunity_interests')
      .select('*')
      .eq('funding_opportunity_id', fundingOpportunityId)
      .eq('user_id', user.id)
      .single()

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to fetch user interest: ${error.message}`)
    }

    return data
  }

  /**
   * Set or update user's interest in a funding opportunity
   */
  static async setUserInterest(
    fundingOpportunityId: string,
    isInterested: boolean,
    wantsCollaboration: boolean,
    note?: string
  ): Promise<any> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User must be authenticated to express interest')
    }

    const { data, error } = await supabase
      .from('funding_opportunity_interests')
      .upsert({
        funding_opportunity_id: fundingOpportunityId,
        user_id: user.id,
        is_interested: isInterested,
        wants_collaboration: wantsCollaboration,
        note: note || null
      }, {
        onConflict: 'funding_opportunity_id,user_id'
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to set user interest: ${error.message}`)
    }

    return data
  }

  /**
   * Remove user's interest in a funding opportunity
   */
  static async removeUserInterest(fundingOpportunityId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User must be authenticated to remove interest')
    }

    const { error } = await supabase
      .from('funding_opportunity_interests')
      .delete()
      .eq('funding_opportunity_id', fundingOpportunityId)
      .eq('user_id', user.id)

    if (error) {
      throw new Error(`Failed to remove user interest: ${error.message}`)
    }
  }

  /**
   * Check if user can add funding opportunities
   */
  static async canUserAddFunding(): Promise<boolean> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return false
    }

    const { data, error } = await supabase
      .from('profiles')
      .select('can_add_funding_opportunities')
      .eq('id', user.id)
      .single()

    if (error) {
      return false
    }

    return data?.can_add_funding_opportunities ?? true
  }

  /**
   * Get funding opportunity statistics
   */
  static async getFundingStats(): Promise<{ liveOpportunities: number; totalOpportunities: number }> {
    try {
      const { data: allData, error: allError } = await supabase
        .from('funding_opportunities')
        .select('id, deadline_date')

      if (allError) {
        throw new Error(`Failed to fetch funding opportunities: ${allError.message}`)
      }

      const totalCount = allData?.length || 0;
      
      // Count live opportunities (no deadline or deadline is today or later)
      const currentDate = new Date().toISOString().split('T')[0];
      const liveCount = allData?.filter(record => 
        !record.deadline_date || record.deadline_date >= currentDate
      ).length || 0;

      return {
        liveOpportunities: liveCount,
        totalOpportunities: totalCount
      };
    } catch (error) {
      console.error('Error fetching funding stats:', error)
      return {
        liveOpportunities: 0,
        totalOpportunities: 0
      }
    }
  }
}
