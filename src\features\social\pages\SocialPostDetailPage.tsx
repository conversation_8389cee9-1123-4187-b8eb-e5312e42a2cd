import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import SocialPost from '../components/SocialPost';
import { SocialService } from '../services/socialService';
import type { SocialPostWithAuthor } from '../types/social.types';

const SocialPostDetailPage: React.FC = () => {
  const { postId } = useParams<{ postId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  
  const [post, setPost] = useState<SocialPostWithAuthor | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load the post
  useEffect(() => {
    const loadPost = async () => {
      if (!postId) {
        setError('Post ID is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const postData = await SocialService.getPost(postId);
        setPost(postData);
      } catch (err) {
        console.error('Error loading post:', err);
        setError(err instanceof Error ? err.message : 'Failed to load post');
        
        toast({
          title: "Error",
          description: "Failed to load post. It may have been deleted or you don't have permission to view it.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadPost();
  }, [postId, toast]);

  // Handle post updates
  const handlePostUpdate = (updatedPostId: string) => {
    if (updatedPostId === postId) {
      // Reload the post to get updated data
      const reloadPost = async () => {
        try {
          const postData = await SocialService.getPost(postId!);
          setPost(postData);
        } catch (err) {
          console.error('Error reloading post:', err);
        }
      };
      reloadPost();
    }
  };

  // Handle post deletion
  const handlePostDelete = (deletedPostId: string) => {
    if (deletedPostId === postId) {
      toast({
        title: "Post deleted",
        description: "This post has been deleted. Redirecting to social feed..."
      });
      
      // Redirect to social feed after a short delay
      setTimeout(() => {
        navigate('/social');
      }, 2000);
    }
  };

  // Handle reaction changes
  const handleReactionChange = (changedPostId: string) => {
    if (changedPostId === postId) {
      // Reload the post to get updated reaction counts
      const reloadPost = async () => {
        try {
          const postData = await SocialService.getPost(postId!);
          setPost(postData);
        } catch (err) {
          console.error('Error reloading post:', err);
        }
      };
      reloadPost();
    }
  };

  // Handle back navigation
  const handleBack = () => {
    navigate('/social');
  };

  // Scroll to comment if hash is present
  useEffect(() => {
    if (post && window.location.hash) {
      const commentId = window.location.hash.replace('#comment-', '');
      if (commentId) {
        // Small delay to ensure the comment is rendered
        setTimeout(() => {
          const element = document.getElementById(`comment-${commentId}`);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }, 500);
      }
    }
  }, [post]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Back Button */}
          <Button
            variant="ghost"
            onClick={handleBack}
            className="mb-6"
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Feed
          </Button>

          {/* Loading State */}
          <Card>
            <CardContent className="p-8 text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">Loading post...</p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Back Button */}
          <Button
            variant="ghost"
            onClick={handleBack}
            className="mb-6"
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Feed
          </Button>

          {/* Error State */}
          <Card>
            <CardContent className="p-8 text-center">
              <h2 className="text-xl font-semibold mb-2">Post Not Found</h2>
              <p className="text-muted-foreground mb-4">
                {error || 'This post may have been deleted or you don\'t have permission to view it.'}
              </p>
              <Button onClick={handleBack}>
                Return to Social Feed
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={handleBack}
          className="mb-6"
        >
          <ArrowLeft size={16} className="mr-2" />
          Back to Feed
        </Button>

        {/* Post Detail */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold mb-2">Post Details</h1>
          <p className="text-muted-foreground">
            View and interact with this social post and its comments.
          </p>
        </div>

        {/* Post Component */}
        <SocialPost
          post={post}
          onPostUpdate={handlePostUpdate}
          onPostDelete={handlePostDelete}
          onReactionChange={handleReactionChange}
        />
      </div>
    </div>
  );
};

export default SocialPostDetailPage;
