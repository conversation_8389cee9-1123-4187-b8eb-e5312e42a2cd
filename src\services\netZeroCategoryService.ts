// Net-Zero Category Service for managing categories and user/business associations
import { supabase } from '@/integrations/supabase/client';
import type {
  NetZeroCategory,
  NetZeroSubcategory,
  NetZeroCategoryWithSubcategories,
  NetZeroSubcategoryWithCategory,
  ProfileNetZeroInterest,
  BusinessNetZeroCategory,
  NewProfileNetZeroInterest,
  NewBusinessNetZeroCategory,
  NetZeroCategoryHierarchy,
  UserInterestsResponse,
  BusinessCategoriesResponse,
  CategoryBusinessCount
} from '@/types/netzero-categories.types';

export class NetZeroCategoryService {
  /**
   * Fetch all categories with their subcategories
   */
  static async getAllCategoriesWithSubcategories(): Promise<NetZeroCategoryWithSubcategories[]> {
    const { data: categories, error: categoriesError } = await supabase
      .from('netzero_categories')
      .select('*')
      .order('sort_order');

    if (categoriesError) {
      throw new Error(`Failed to fetch categories: ${categoriesError.message}`);
    }

    const { data: subcategories, error: subcategoriesError } = await supabase
      .from('netzero_subcategories')
      .select('*')
      .order('category_id, sort_order');

    if (subcategoriesError) {
      throw new Error(`Failed to fetch subcategories: ${subcategoriesError.message}`);
    }

    // Group subcategories by category
    const categoriesWithSubcategories: NetZeroCategoryWithSubcategories[] = categories.map(category => ({
      ...category,
      subcategories: subcategories.filter(sub => sub.category_id === category.id)
    }));

    return categoriesWithSubcategories;
  }

  /**
   * Get categories as a hierarchical structure for UI components
   */
  static async getCategoryHierarchy(): Promise<NetZeroCategoryHierarchy> {
    const categoriesWithSubs = await this.getAllCategoriesWithSubcategories();
    
    const hierarchy: NetZeroCategoryHierarchy = {};
    
    categoriesWithSubs.forEach(category => {
      hierarchy[category.id] = {
        id: category.id,
        name: category.name,
        description: category.description,
        sort_order: category.sort_order,
        subcategories: {}
      };
      
      category.subcategories.forEach(subcategory => {
        hierarchy[category.id].subcategories[subcategory.id] = {
          id: subcategory.id,
          name: subcategory.name,
          description: subcategory.description,
          sort_order: subcategory.sort_order
        };
      });
    });
    
    return hierarchy;
  }

  /**
   * Get user's net-zero interests
   */
  static async getUserInterests(userId: string): Promise<UserInterestsResponse> {
    const { data, error } = await supabase
      .from('profile_netzero_interests')
      .select(`
        subcategory_id,
        is_primary,
        netzero_subcategories (
          id,
          name,
          description,
          sort_order,
          netzero_categories (
            id,
            name,
            description,
            sort_order
          )
        )
      `)
      .eq('profile_id', userId);

    if (error) {
      throw new Error(`Failed to fetch user interests: ${error.message}`);
    }

    let primarySubcategoryId: string | undefined = undefined;
    const interests: NetZeroSubcategoryWithCategory[] = data.map(item => {
      if (item.is_primary) primarySubcategoryId = item.subcategory_id;
      return {
        ...(item.netzero_subcategories as any),
        category: (item.netzero_subcategories as any).netzero_categories
      };
    });

    return { interests, primarySubcategoryId };
  }

  /**
   * Update user's net-zero interests
   */
  static async updateUserInterests(userId: string, subcategoryIds: string[], primarySubcategoryId?: string): Promise<void> {
    // First, delete existing interests
    const { error: deleteError } = await supabase
      .from('profile_netzero_interests')
      .delete()
      .eq('profile_id', userId);

    if (deleteError) {
      throw new Error(`Failed to delete existing interests: ${deleteError.message}`);
    }

    // Then, insert new interests
    if (subcategoryIds.length > 0) {
      const newInterests: NewProfileNetZeroInterest[] = subcategoryIds.map(subcategoryId => ({
        profile_id: userId,
        subcategory_id: subcategoryId,
        is_primary: primarySubcategoryId === subcategoryId
      }));

      const { error: insertError } = await supabase
        .from('profile_netzero_interests')
        .insert(newInterests);

      if (insertError) {
        throw new Error(`Failed to insert new interests: ${insertError.message}`);
      }
    }
  }

  /**
   * Get business's net-zero categories
   */
  static async getBusinessCategories(businessId: string): Promise<BusinessCategoriesResponse> {
    const { data, error } = await supabase
      .from('business_netzero_categories')
      .select(`
        subcategory_id,
        is_primary,
        netzero_subcategories (
          id,
          name,
          description,
          sort_order,
          netzero_categories (
            id,
            name,
            description,
            sort_order
          )
        )
      `)
      .eq('business_id', businessId);

    if (error) {
      throw new Error(`Failed to fetch business categories: ${error.message}`);
    }

    const categories: NetZeroSubcategoryWithCategory[] = data.map(item => ({
      ...(item.netzero_subcategories as any),
      category: (item.netzero_subcategories as any).netzero_categories
    }));

    const primaryCategory = data.find(item => item.is_primary);
    const primary_category = primaryCategory ? {
      ...(primaryCategory.netzero_subcategories as any),
      category: (primaryCategory.netzero_subcategories as any).netzero_categories
    } : undefined;

    return { categories, primary_category };
  }

  /**
   * Update business's net-zero categories
   */
  static async updateBusinessCategories(
    businessId: string, 
    subcategoryIds: string[], 
    primarySubcategoryId?: string
  ): Promise<void> {
    // First, delete existing categories
    const { error: deleteError } = await supabase
      .from('business_netzero_categories')
      .delete()
      .eq('business_id', businessId);

    if (deleteError) {
      throw new Error(`Failed to delete existing categories: ${deleteError.message}`);
    }

    // Then, insert new categories
    if (subcategoryIds.length > 0) {
      const newCategories: NewBusinessNetZeroCategory[] = subcategoryIds.map(subcategoryId => ({
        business_id: businessId,
        subcategory_id: subcategoryId,
        is_primary: subcategoryId === primarySubcategoryId
      }));

      const { error: insertError } = await supabase
        .from('business_netzero_categories')
        .insert(newCategories);

      if (insertError) {
        throw new Error(`Failed to insert new categories: ${insertError.message}`);
      }
    }
  }

  /**
   * Get subcategory details by ID
   */
  static async getSubcategoryById(subcategoryId: string): Promise<NetZeroSubcategoryWithCategory | null> {
    const { data, error } = await supabase
      .from('netzero_subcategories')
      .select(`
        *,
        netzero_categories (*)
      `)
      .eq('id', subcategoryId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to fetch subcategory: ${error.message}`);
    }

    return {
      ...data,
      category: data.netzero_categories as NetZeroCategory
    };
  }

  /**
   * Search subcategories by name
   */
  static async searchSubcategories(query: string): Promise<NetZeroSubcategoryWithCategory[]> {
    const { data, error } = await supabase
      .from('netzero_subcategories')
      .select(`
        *,
        netzero_categories (*)
      `)
      .ilike('name', `%${query}%`)
      .order('name');

    if (error) {
      throw new Error(`Failed to search subcategories: ${error.message}`);
    }

    return data.map(item => ({
      ...item,
      category: item.netzero_categories as NetZeroCategory
    }));
  }

  /**
   * Get business counts for each main Net Zero category
   */
  static async getCategoryBusinessCounts(): Promise<CategoryBusinessCount[]> {
    const { data, error } = await supabase
      .from('netzero_categories')
      .select(`
        id,
        name,
        description,
        sort_order,
        netzero_subcategories!inner (
          id,
          business_netzero_categories!inner (
            business_id
          )
        )
      `)
      .order('sort_order');

    if (error) {
      throw new Error(`Failed to fetch category business counts: ${error.message}`);
    }

    // Process the data to count unique businesses per category
    const categoryBusinessCounts = data.map(category => {
      // Get all unique business IDs for this category
      const businessIds = new Set<string>();

      category.netzero_subcategories.forEach(subcategory => {
        subcategory.business_netzero_categories.forEach(businessCategory => {
          businessIds.add(businessCategory.business_id);
        });
      });

      return {
        categoryId: category.id,
        categoryName: category.name,
        description: category.description,
        businessCount: businessIds.size
      };
    });

    return categoryBusinessCounts;
  }
}
