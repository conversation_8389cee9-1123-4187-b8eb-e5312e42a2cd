-- Clean up mention notification function (remove debugging)
-- This migration restores the clean notification function without debug statements

-- Restore the clean post mention notification function
CREATE OR REPLACE FUNCTION notify_mentioned_users_in_post()
RETURNS TRIGGER AS $$
DECLARE
    mentioned_user_record RECORD;
    post_record RECORD;
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
BEGIN
    -- Get post details
    SELECT p.*, 
           author.first_name as author_first_name,
           author.last_name as author_last_name
    INTO post_record
    FROM public.social_posts p
    JOIN public.profiles author ON p.user_id = author.id
    WHERE p.id = NEW.post_id;
    
    -- Get mentioned user details
    SELECT * INTO mentioned_user_record
    FROM public.profiles
    WHERE id = NEW.mentioned_user_id;
    
    -- Only create notification if user allows mentions and is not mentioning themselves
    IF mentioned_user_record.allow_mentions = true AND NEW.mentioned_user_id != NEW.mentioned_by_user_id THEN
        -- Create notification title and message
        notification_title := COALESCE(post_record.author_first_name, 'Someone') || ' mentioned you in a post';
        notification_message := 'You were mentioned in a social post: "' || 
                               LEFT(post_record.content, 100) || 
                               CASE WHEN LENGTH(post_record.content) > 100 THEN '...' ELSE '' END || '"';
        
        -- Create action URL to the post
        action_url := '/social/post/' || NEW.post_id::TEXT;
        
        -- Create notification data
        notification_data := jsonb_build_object(
            'post_id', NEW.post_id,
            'mentioned_by_user_id', NEW.mentioned_by_user_id,
            'mention_text', NEW.mention_text,
            'post_content_preview', LEFT(post_record.content, 200)
        );
        
        -- Create notification
        PERFORM create_notification(
            NEW.mentioned_user_id,
            'social_mention'::notification_type_enum,
            notification_title,
            notification_message,
            NEW.post_id,
            'social_post',
            action_url,
            notification_data
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
