import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ir<PERSON>, Reply, Edit, Trash2, MoreHorizontal } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import MentionInput from './MentionInput';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useComments, useCommentForm } from '../hooks/useComments';
import { supabase } from '@/integrations/supabase/client';
import UserProfileLink from './UserProfileLink';
import CommentReactions from './CommentReactionsSimple';
import type { SocialCommentWithAuthor } from '../types/social.types';

interface UserProfile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  avatar_url: string | null;
}

// User profile interface for displaying avatar in comment form

interface CommentSectionProps {
  postId: string;
}

interface CommentItemProps {
  comment: SocialCommentWithAuthor;
  onCommentUpdate: () => void;
  onReply: (parentId: string) => void;
  depth?: number;
}

const CommentItem: React.FC<CommentItemProps> = ({ 
  comment, 
  onCommentUpdate, 
  onReply, 
  depth = 0 
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(comment.content);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const isOwnComment = user?.id === comment.user_id;
  const canReply = depth < 3; // Max depth of 3 (0, 1, 2, 3)

  const { updateComment, deleteComment } = useComments(comment.post_id);

  const handleEditSave = async () => {
    if (!editContent.trim() || editContent === comment.content) {
      setIsEditing(false);
      setEditContent(comment.content);
      return;
    }

    try {
      setIsUpdating(true);
      await updateComment(comment.id, editContent.trim());
      setIsEditing(false);
      onCommentUpdate();
      
      toast({
        title: "Comment updated",
        description: "Your comment has been updated successfully."
      });
    } catch (error) {
      console.error('Error updating comment:', error);
      toast({
        title: "Error",
        description: "Failed to update comment. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await deleteComment(comment.id);
      setShowDeleteDialog(false);
      onCommentUpdate();
      
      toast({
        title: "Comment deleted",
        description: "Your comment has been deleted successfully."
      });
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast({
        title: "Error",
        description: "Failed to delete comment. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className={`${depth > 0 ? 'ml-6 mt-3' : 'mt-3'}`}>
      <Card>
        <CardContent className="p-3">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center overflow-hidden">
              {comment.author.avatar_url ? (
                <img
                  src={comment.author.avatar_url}
                  alt={`${comment.author.first_name || 'User'} avatar`}
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-xs font-medium">
                  {comment.author.first_name?.[0] || comment.author.email?.[0]?.toUpperCase() || 'U'}
                </span>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <div className="flex flex-col">
                  <div className="flex items-center space-x-2">
                    <UserProfileLink
                      userId={comment.user_id}
                      firstName={comment.author.first_name}
                      lastName={comment.author.last_name}
                      jobTitle={comment.author.job_title}
                      organisationName={comment.author.organisation_name}
                      size="sm"
                      showJobTitle={false}
                      showOrganisation={false}
                    />
                    <span className="text-xs text-muted-foreground">
                      {new Date(comment.created_at).toLocaleDateString()}
                      {comment.edited_at && ' (edited)'}
                    </span>
                  </div>
                  {comment.author.job_title && (
                    <span className="text-xs text-muted-foreground">
                      {comment.author.job_title}
                    </span>
                  )}
                  {comment.author.organisation_name && (
                    <span className="text-xs text-muted-foreground">
                      {comment.author.organisation_name}
                    </span>
                  )}
                </div>
                {isOwnComment && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                        <MoreHorizontal size={12} />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setIsEditing(true)}>
                        <Edit size={12} className="mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => setShowDeleteDialog(true)}
                        className="text-destructive"
                      >
                        <Trash2 size={12} className="mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>

              {/* Comment Content */}
              {isEditing ? (
                <div className="mt-2">
                  <MentionInput
                    value={editContent}
                    onChange={setEditContent}
                    className="min-h-[60px] resize-none text-xs"
                    maxLength={1000}
                    rows={3}
                  />
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-xs text-muted-foreground">
                      {editContent.length}/1000 characters
                    </span>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setIsEditing(false);
                          setEditContent(comment.content);
                        }}
                        disabled={isUpdating}
                        className="h-6 text-xs"
                      >
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        onClick={handleEditSave}
                        disabled={!editContent.trim() || isUpdating}
                        className="h-6 text-xs"
                      >
                        {isUpdating ? 'Saving...' : 'Save'}
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <>
                  <p className="text-xs mt-1 whitespace-pre-wrap">{comment.content}</p>
                  <div className="flex items-center justify-between mt-2">
                    <div className="flex items-center space-x-2">
                      {canReply && user && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 px-2 text-xs"
                          onClick={() => onReply(comment.id)}
                        >
                          <Reply size={12} className="mr-1" />
                          Reply
                        </Button>
                      )}
                    </div>
                    <CommentReactions
                      comment={comment}
                      onReactionChange={onCommentUpdate}
                    />
                  </div>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Nested Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div>
          {comment.replies.map(reply => (
            <CommentItem
              key={reply.id}
              comment={reply}
              onCommentUpdate={onCommentUpdate}
              onReply={onReply}
              depth={depth + 1}
            />
          ))}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Comment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this comment? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

const CommentSection: React.FC<CommentSectionProps> = ({ postId }) => {
  const { user } = useAuth();
  const { comments, loading, createComment, refresh } = useComments(postId);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);

  // Fetch user profile data
  useEffect(() => {
    if (user?.id) {
      const fetchProfile = async () => {
        try {
          const { data, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();

          if (error) {
            console.error('Error fetching profile:', error);
          } else {
            setUserProfile(data);
          }
        } catch (error) {
          console.error('Error fetching profile:', error);
        }
      };

      fetchProfile();
    }
  }, [user?.id]);

  const {
    content,
    setContent,
    handleSubmit,
    canSubmit,
    isSubmitting,
    reset
  } = useCommentForm(async (data) => {
    await createComment({
      ...data,
      postId,
      parentCommentId: replyingTo || undefined
    });
    setReplyingTo(null);
  });

  const handleReply = (parentId: string) => {
    setReplyingTo(parentId);
  };

  const handleCommentUpdate = () => {
    refresh();
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="p-4 text-center">
          <p className="text-sm text-muted-foreground">
            Sign in to join the conversation
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Comment Form */}
      <Card>
        <CardContent className="p-4">
          <form onSubmit={handleSubmit} className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center overflow-hidden">
                {userProfile?.avatar_url ? (
                  <img
                    src={userProfile.avatar_url}
                    alt={`${userProfile.first_name || 'User'} avatar`}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <span className="text-xs font-medium">
                    {userProfile?.first_name?.[0] || user?.email?.[0]?.toUpperCase() || 'U'}
                  </span>
                )}
              </div>
              <div className="flex-1">
                <MentionInput
                  placeholder={replyingTo ? "Write a reply..." : "Write a comment..."}
                  value={content}
                  onChange={setContent}
                  className="min-h-[60px] resize-none text-sm"
                  maxLength={1000}
                  rows={3}
                />
                <div className="flex justify-between items-center mt-2">
                  <span className="text-xs text-muted-foreground">
                    {content.length}/1000 characters
                  </span>
                  <div className="flex space-x-2">
                    {replyingTo && (
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setReplyingTo(null);
                          reset();
                        }}
                        className="h-7 text-xs"
                      >
                        Cancel Reply
                      </Button>
                    )}
                    <Button
                      type="submit"
                      size="sm"
                      disabled={!canSubmit}
                      className="h-7 text-xs"
                    >
                      {isSubmitting ? 'Posting...' : replyingTo ? 'Reply' : 'Comment'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Comments List */}
      {loading ? (
        <div className="text-center py-4">
          <p className="text-sm text-muted-foreground">Loading comments...</p>
        </div>
      ) : comments.length > 0 ? (
        <div>
          {comments.map(comment => (
            <CommentItem
              key={comment.id}
              comment={comment}
              onCommentUpdate={handleCommentUpdate}
              onReply={handleReply}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-4">
          <p className="text-sm text-muted-foreground">
            No comments yet. Be the first to comment!
          </p>
        </div>
      )}
    </div>
  );
};

export default CommentSection;
