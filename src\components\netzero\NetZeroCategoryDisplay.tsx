import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Leaf, Star } from 'lucide-react';
import type { NetZeroSubcategoryWithCategory } from '@/types/netzero-categories.types';
import { getNetZeroCategoryTheme, NetZeroCategoryIcon } from '@/utils/netZeroCategoryIcons';

interface NetZeroCategoryDisplayProps {
  categories: NetZeroSubcategoryWithCategory[];
  primaryCategory?: NetZeroSubcategoryWithCategory;
  title?: string;
  description?: string;
  showPrimary?: boolean;
  variant?: 'card' | 'inline' | 'badges-only';
  className?: string;
}

export const NetZeroCategoryDisplay: React.FC<NetZeroCategoryDisplayProps> = ({
  categories,
  primaryCategory,
  title = "Net-Zero Categories",
  description,
  showPrimary = false,
  variant = 'card',
  className = ""
}) => {
  if (categories.length === 0) {
    return null;
  }

  // Group categories by main category
  const groupedCategories = categories.reduce((acc, subcategory) => {
    const categoryName = subcategory.category.name;
    if (!acc[categoryName]) {
      acc[categoryName] = [];
    }
    acc[categoryName].push(subcategory);
    return acc;
  }, {} as Record<string, NetZeroSubcategoryWithCategory[]>);

  const renderBadges = () => (
    <div className="flex flex-wrap gap-2">
      {Object.entries(groupedCategories).map(([categoryName, subcategories]) => {
        const theme = getNetZeroCategoryTheme(categoryName);
        return (
          <div key={categoryName} className="flex flex-wrap gap-1">
            {subcategories.map((subcategory) => {
              const isPrimary = showPrimary && primaryCategory?.id === subcategory.id;
              // For primary, use themed border with no background color
              return (
                <div key={subcategory.id} className="flex flex-col gap-1">
                  {isPrimary && (
                    <span className="text-xs font-medium text-gray-600 px-1">Primary</span>
                  )}
                  <Badge
                    variant="outline"
                    className={`flex items-center gap-1 ${isPrimary 
                      ? `border-[3px] ${theme.borderColor} ${theme.textColor}` 
                      : `border ${theme.borderColor} ${theme.textColor}`
                    }`}
                  >
                    <NetZeroCategoryIcon categoryName={categoryName} size="sm" className="w-3 h-3" />
                    {subcategory.name}
                  </Badge>
                </div>
              );
            })}
          </div>
        );
      })}
    </div>
  );

  const renderGroupedList = () => {
    // Separate primary category from others
    const primaryCategoryForDisplay = showPrimary && primaryCategory;
    const otherCategories = primaryCategoryForDisplay 
      ? categories.filter(cat => cat.id !== primaryCategory.id)
      : categories;

    // Group other categories by main category
    const otherGroupedCategories = otherCategories.reduce((acc, subcategory) => {
      const categoryName = subcategory.category.name;
      if (!acc[categoryName]) {
        acc[categoryName] = [];
      }
      acc[categoryName].push(subcategory);
      return acc;
    }, {} as Record<string, NetZeroSubcategoryWithCategory[]>);

    return (
      <div className="space-y-4">
        {/* Primary Area of Interest Section */}
        {primaryCategoryForDisplay && (
          <div className="space-y-2">
            <h4 className="font-semibold text-sm">Primary Area of Interest:</h4>
            <div className="flex items-center gap-2">
              {(() => {
                const theme = getNetZeroCategoryTheme(primaryCategoryForDisplay.category.name);
                return (
                  <Badge
                    variant="outline"
                    className={`flex items-center gap-1 border-[3px] ${theme.borderColor} ${theme.textColor}`}
                  >
                    <NetZeroCategoryIcon categoryName={primaryCategoryForDisplay.category.name} size="sm" className="w-3 h-3" />
                    {primaryCategoryForDisplay.name}
                  </Badge>
                );
              })()}
            </div>
          </div>
        )}

        {/* Other Categories */}
        {Object.keys(otherGroupedCategories).length > 0 && (
          <div className="space-y-2">
            <h4 className="font-semibold text-sm">
              Other Areas of Interest ({otherCategories.length}):
            </h4>
            <div className="space-y-3">
              {Object.entries(otherGroupedCategories).map(([categoryName, subcategories]) => {
                const theme = getNetZeroCategoryTheme(categoryName);
                return (
                  <div key={categoryName}>
                    <h5 className={`font-medium text-xs mb-1 flex items-center gap-2 ${theme.textColor} ml-2`}>
                      <NetZeroCategoryIcon categoryName={categoryName} size="sm" className="w-3 h-3" />
                      {categoryName}
                    </h5>
                    <div className="flex flex-wrap gap-2 ml-6">
                      {subcategories.map((subcategory) => (
                        <Badge
                          key={subcategory.id}
                          variant="outline"
                          className={`flex items-center gap-1 border ${theme.borderColor} ${theme.textColor}`}
                        >
                          <NetZeroCategoryIcon categoryName={categoryName} size="sm" className="w-3 h-3" />
                          {subcategory.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (variant === 'badges-only') {
    return (
      <div className={className}>
        {renderBadges()}
      </div>
    );
  }

  if (variant === 'inline') {
    return (
      <div className={className}>
        <div className="space-y-3">
          {title && (
            <h3 className="font-medium flex items-center gap-2">
              <Leaf className="w-4 h-4 text-green-600" />
              {title}
            </h3>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
          {renderGroupedList()}
        </div>
      </div>
    );
  }

  // Default card variant
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Leaf className="w-5 h-5 text-green-600" />
          {title}
        </CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        {renderGroupedList()}
      </CardContent>
    </Card>
  );
};

// Simplified component for just showing category names as text
interface NetZeroCategoryListProps {
  categories: NetZeroSubcategoryWithCategory[];
  primaryCategory?: NetZeroSubcategoryWithCategory;
  showPrimary?: boolean;
  separator?: string;
  maxDisplay?: number;
  className?: string;
}

export const NetZeroCategoryList: React.FC<NetZeroCategoryListProps> = ({
  categories,
  primaryCategory,
  showPrimary = false,
  separator = ', ',
  maxDisplay,
  className = ""
}) => {
  if (categories.length === 0) {
    return <span className={className}>No categories selected</span>;
  }

  let displayCategories = categories;
  if (maxDisplay && categories.length > maxDisplay) {
    displayCategories = categories.slice(0, maxDisplay);
  }

  const categoryText = displayCategories.map((category) => {
    const isPrimary = showPrimary && primaryCategory?.id === category.id;
    return isPrimary ? `${category.name} (Primary)` : category.name;
  }).join(separator);

  const remainingCount = categories.length - displayCategories.length;

  return (
    <span className={className}>
      {categoryText}
      {remainingCount > 0 && ` and ${remainingCount} more`}
    </span>
  );
};

// Hook for getting category display data
export const useNetZeroCategoryDisplay = (
  categories: NetZeroSubcategoryWithCategory[],
  primaryCategory?: NetZeroSubcategoryWithCategory
) => {
  const totalCount = categories.length;
  const categoryNames = categories.map(cat => cat.name);
  const mainCategories = [...new Set(categories.map(cat => cat.category.name))];
  
  const summary = {
    totalCount,
    categoryNames,
    mainCategories,
    primaryCategoryName: primaryCategory?.name,
    hasMultipleMainCategories: mainCategories.length > 1
  };

  return summary;
};
