// Test interest matching notifications
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'http://127.0.0.1:54321'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testNotifications() {
  console.log('🔔 Testing interest matching notifications...\n')

  try {
    // Test 1: Check if users have interests set up
    console.log('1. Checking user interests...')
    const { data: userInterests, error: interestError } = await supabase
      .from('profile_netzero_interests')
      .select(`
        profile_id,
        subcategory_id,
        profiles!inner(first_name, last_name, notify_interest_matches, notify_new_events),
        netzero_subcategories!inner(name, netzero_categories!inner(name))
      `)
    
    if (interestError) {
      console.log('❌ Error fetching user interests:', interestError.message)
    } else {
      console.log(`✅ Found ${userInterests?.length || 0} user interests`)
      userInterests?.forEach(interest => {
        console.log(`   - ${interest.profiles.first_name} ${interest.profiles.last_name}: ${interest.netzero_subcategories.netzero_categories.name} > ${interest.netzero_subcategories.name}`)
        console.log(`     Notifications: interest_matches=${interest.profiles.notify_interest_matches}, new_events=${interest.profiles.notify_new_events}`)
      })
    }

    // Test 2: Check notification triggers exist
    console.log('\n2. Checking notification triggers...')
    const { data: triggers, error: triggerError } = await supabase
      .from('information_schema.triggers')
      .select('trigger_name, event_object_table')
      .like('trigger_name', '%notify%')
    
    if (triggerError) {
      console.log('❌ Error checking triggers:', triggerError.message)
    } else {
      console.log(`✅ Found ${triggers?.length || 0} notification triggers`)
      triggers?.forEach(trigger => {
        console.log(`   - ${trigger.trigger_name} on ${trigger.event_object_table}`)
      })
    }

    // Test 3: Check if net zero categories exist
    console.log('\n3. Checking net zero categories...')
    const { data: categories, error: categoryError } = await supabase
      .from('netzero_subcategories')
      .select('id, name, netzero_categories!inner(name)')
      .limit(5)
    
    if (categoryError) {
      console.log('❌ Error fetching categories:', categoryError.message)
    } else {
      console.log(`✅ Found ${categories?.length || 0} categories (showing first 5)`)
      categories?.forEach(cat => {
        console.log(`   - ${cat.netzero_categories.name} > ${cat.name} (${cat.id})`)
      })
    }

    // Test 4: Create a test event with categories
    console.log('\n4. Creating test event...')
    
    // First get a category ID
    const { data: testCategory } = await supabase
      .from('netzero_subcategories')
      .select('id, name')
      .limit(1)
      .single()
    
    if (!testCategory) {
      console.log('❌ No categories found to test with')
      return
    }

    // Create test event
    const { data: newEvent, error: eventError } = await supabase
      .from('events')
      .insert({
        name: 'Test Event for Notifications',
        description: 'Testing interest matching notifications',
        start_date: '2025-08-01',
        location_type: 'online',
        created_by_user_id: 'bb3e097a-2775-4e7b-a767-15f64a95ff09' // Your user ID
      })
      .select()
      .single()
    
    if (eventError) {
      console.log('❌ Error creating event:', eventError.message)
      return
    }

    console.log(`✅ Created test event: ${newEvent.name} (${newEvent.id})`)

    // Add category to event
    const { error: categoryLinkError } = await supabase
      .from('event_netzero_categories')
      .insert({
        event_id: newEvent.id,
        subcategory_id: testCategory.id
      })
    
    if (categoryLinkError) {
      console.log('❌ Error linking category to event:', categoryLinkError.message)
    } else {
      console.log(`✅ Linked category "${testCategory.name}" to event`)
    }

    // Test 5: Check if notifications were created
    console.log('\n5. Checking for new notifications...')
    await new Promise(resolve => setTimeout(resolve, 1000)) // Wait a bit for triggers
    
    const { data: notifications, error: notificationError } = await supabase
      .from('notifications')
      .select('*')
      .eq('type', 'event_interest_match')
      .order('created_at', { ascending: false })
      .limit(5)
    
    if (notificationError) {
      console.log('❌ Error fetching notifications:', notificationError.message)
    } else {
      console.log(`✅ Found ${notifications?.length || 0} event interest notifications`)
      notifications?.forEach(notif => {
        console.log(`   - ${notif.title} for user ${notif.user_id}`)
        console.log(`     Message: ${notif.message}`)
      })
    }

    // Clean up test event
    console.log('\n6. Cleaning up test event...')
    await supabase.from('events').delete().eq('id', newEvent.id)
    console.log('✅ Test event cleaned up')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

testNotifications()
